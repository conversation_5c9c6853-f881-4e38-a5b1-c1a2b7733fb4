{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_path", "_helperModuleTransforms", "_core", "buildPrerequisiteAssignment", "template", "buildWrapper", "_default", "exports", "default", "declare", "api", "options", "_api$assumption", "_api$assumption2", "assertVersion", "globals", "exactGlobals", "allowTopLevelThis", "strict", "strictMode", "noInterop", "importInterop", "constantReexports", "assumption", "loose", "enumerableModuleMeta", "buildBrowserInit", "browserGlobals", "filename", "moduleName", "moduleNameOrBasename", "value", "basename", "extname", "globalToAssign", "t", "memberExpression", "identifier", "toIdentifier", "initAssignments", "globalName", "members", "split", "slice", "reduce", "accum", "curr", "push", "GLOBAL_REFERENCE", "cloneNode", "expressionStatement", "assignmentExpression", "buildBrowserArg", "source", "globalRef", "requireName", "name", "visitor", "Program", "exit", "path", "isModule", "getModuleName", "file", "opts", "moduleNameLiteral", "stringLiteral", "meta", "headers", "rewriteModuleStatementsAndPrepareHeader", "amdArgs", "commonjsArgs", "browserArgs", "importNames", "hasExports", "exportName", "metadata", "callExpression", "isSideEffectImport", "interop", "wrapInterop", "header", "loc", "buildNamespaceInitStatements", "ensureStatementsHoisted", "unshiftContainer", "body", "directives", "node", "umdWrapper", "pushContainer", "MODULE_NAME", "AMD_ARGUMENTS", "arrayExpression", "COMMONJS_ARGUMENTS", "BROWSER_ARGUMENTS", "IMPORT_NAMES", "GLOBAL_TO_ASSIGN", "umdFactory", "get"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\nimport { basename, extname } from \"path\";\nimport {\n  isModule,\n  rewriteModuleStatementsAndPrepareHeader,\n  type RewriteModuleStatementsAndPrepareHeaderOptions,\n  hasExports,\n  isSideEffectImport,\n  buildNamespaceInitStatements,\n  ensureStatementsHoisted,\n  wrapInterop,\n  getModuleName,\n} from \"@babel/helper-module-transforms\";\nimport type { PluginOptions } from \"@babel/helper-module-transforms\";\nimport { types as t, template, type NodePath } from \"@babel/core\";\n\nconst buildPrerequisiteAssignment = template(`\n  GLOBAL_REFERENCE = GLOBAL_REFERENCE || {}\n`);\n// Note: we avoid comparing typeof results with \"object\" or \"symbol\" otherwise\n// they will be processed by `transform-typeof-symbol`, which in return could\n// cause typeof helper used before declaration\nconst buildWrapper = template(`\n  (function (global, factory) {\n    if (typeof define === \"function\" && define.amd) {\n      define(MODULE_NAME, AMD_ARGUMENTS, factory);\n    } else if (typeof exports !== \"undefined\") {\n      factory(COMMONJS_ARGUMENTS);\n    } else {\n      var mod = { exports: {} };\n      factory(BROWSER_ARGUMENTS);\n\n      GLOBAL_TO_ASSIGN;\n    }\n  })(\n    typeof globalThis !== \"undefined\" ? globalThis\n      : typeof self !== \"undefined\" ? self\n      : this,\n    function(IMPORT_NAMES) {\n  })\n`);\n\nexport interface Options extends PluginOptions {\n  allowTopLevelThis?: boolean;\n  exactGlobals?: boolean;\n  globals?: Record<string, string>;\n  importInterop?: RewriteModuleStatementsAndPrepareHeaderOptions[\"importInterop\"];\n  loose?: boolean;\n  noInterop?: boolean;\n  strict?: boolean;\n  strictMode?: boolean;\n}\n\nexport default declare((api, options: Options) => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  const {\n    globals,\n    exactGlobals,\n    allowTopLevelThis,\n    strict,\n    strictMode,\n    noInterop,\n    importInterop,\n  } = options;\n\n  const constantReexports =\n    api.assumption(\"constantReexports\") ?? options.loose;\n  const enumerableModuleMeta =\n    api.assumption(\"enumerableModuleMeta\") ?? options.loose;\n\n  /**\n   * Build the assignment statements that initialize the UMD global.\n   */\n  function buildBrowserInit(\n    browserGlobals: Record<string, string>,\n    exactGlobals: boolean,\n    filename: string,\n    moduleName: t.StringLiteral | void,\n  ) {\n    const moduleNameOrBasename = moduleName\n      ? moduleName.value\n      : basename(filename, extname(filename));\n    let globalToAssign = t.memberExpression(\n      t.identifier(\"global\"),\n      t.identifier(t.toIdentifier(moduleNameOrBasename)),\n    );\n    let initAssignments = [];\n\n    if (exactGlobals) {\n      const globalName = browserGlobals[moduleNameOrBasename];\n\n      if (globalName) {\n        initAssignments = [];\n\n        const members = globalName.split(\".\");\n        globalToAssign = members.slice(1).reduce(\n          (accum, curr) => {\n            initAssignments.push(\n              buildPrerequisiteAssignment({\n                GLOBAL_REFERENCE: t.cloneNode(accum),\n              }),\n            );\n            return t.memberExpression(accum, t.identifier(curr));\n          },\n          t.memberExpression(t.identifier(\"global\"), t.identifier(members[0])),\n        );\n      }\n    }\n\n    initAssignments.push(\n      t.expressionStatement(\n        t.assignmentExpression(\n          \"=\",\n          globalToAssign,\n          t.memberExpression(t.identifier(\"mod\"), t.identifier(\"exports\")),\n        ),\n      ),\n    );\n\n    return initAssignments;\n  }\n\n  /**\n   * Build the member expression that reads from a global for a given source.\n   */\n  function buildBrowserArg(\n    browserGlobals: Record<string, string>,\n    exactGlobals: boolean,\n    source: string,\n  ) {\n    let memberExpression: t.MemberExpression;\n    if (exactGlobals) {\n      const globalRef = browserGlobals[source];\n      if (globalRef) {\n        memberExpression = globalRef\n          .split(\".\")\n          .reduce(\n            (accum: t.Identifier | t.MemberExpression, curr) =>\n              t.memberExpression(accum, t.identifier(curr)),\n            t.identifier(\"global\"),\n          ) as t.MemberExpression;\n      } else {\n        memberExpression = t.memberExpression(\n          t.identifier(\"global\"),\n          t.identifier(t.toIdentifier(source)),\n        );\n      }\n    } else {\n      const requireName = basename(source, extname(source));\n      const globalName = browserGlobals[requireName] || requireName;\n      memberExpression = t.memberExpression(\n        t.identifier(\"global\"),\n        t.identifier(t.toIdentifier(globalName)),\n      );\n    }\n    return memberExpression;\n  }\n\n  return {\n    name: \"transform-modules-umd\",\n\n    visitor: {\n      Program: {\n        exit(path) {\n          if (!isModule(path)) return;\n\n          const browserGlobals = globals || {};\n\n          const moduleName = getModuleName(this.file.opts, options);\n          let moduleNameLiteral: void | t.StringLiteral;\n          if (moduleName) moduleNameLiteral = t.stringLiteral(moduleName);\n\n          const { meta, headers } = rewriteModuleStatementsAndPrepareHeader(\n            path,\n            {\n              constantReexports,\n              enumerableModuleMeta,\n              strict,\n              strictMode,\n              allowTopLevelThis,\n              noInterop,\n              importInterop,\n              filename: this.file.opts.filename,\n            },\n          );\n\n          const amdArgs = [];\n          const commonjsArgs = [];\n          const browserArgs = [];\n          const importNames = [];\n\n          if (hasExports(meta)) {\n            amdArgs.push(t.stringLiteral(\"exports\"));\n            commonjsArgs.push(t.identifier(\"exports\"));\n            browserArgs.push(\n              t.memberExpression(t.identifier(\"mod\"), t.identifier(\"exports\")),\n            );\n            importNames.push(t.identifier(meta.exportName));\n          }\n\n          for (const [source, metadata] of meta.source) {\n            amdArgs.push(t.stringLiteral(source));\n            commonjsArgs.push(\n              t.callExpression(t.identifier(\"require\"), [\n                t.stringLiteral(source),\n              ]),\n            );\n            browserArgs.push(\n              buildBrowserArg(browserGlobals, exactGlobals, source),\n            );\n            importNames.push(t.identifier(metadata.name));\n\n            if (!isSideEffectImport(metadata)) {\n              const interop = wrapInterop(\n                path,\n                t.identifier(metadata.name),\n                metadata.interop,\n              );\n              if (interop) {\n                const header = t.expressionStatement(\n                  t.assignmentExpression(\n                    \"=\",\n                    t.identifier(metadata.name),\n                    interop,\n                  ),\n                );\n                // @ts-expect-error todo(flow->ts)\n                header.loc = meta.loc;\n                headers.push(header);\n              }\n            }\n\n            headers.push(\n              ...buildNamespaceInitStatements(\n                meta,\n                metadata,\n                constantReexports,\n              ),\n            );\n          }\n\n          ensureStatementsHoisted(headers);\n          path.unshiftContainer(\"body\", headers);\n\n          const { body, directives } = path.node;\n          path.node.directives = [];\n          path.node.body = [];\n          const umdWrapper = path.pushContainer(\"body\", [\n            buildWrapper({\n              //todo: buildWrapper does not handle void moduleNameLiteral\n              MODULE_NAME: moduleNameLiteral,\n\n              AMD_ARGUMENTS: t.arrayExpression(amdArgs),\n              COMMONJS_ARGUMENTS: commonjsArgs,\n              BROWSER_ARGUMENTS: browserArgs,\n              IMPORT_NAMES: importNames,\n\n              GLOBAL_TO_ASSIGN: buildBrowserInit(\n                browserGlobals,\n                exactGlobals,\n                this.filename || \"unknown\",\n                moduleNameLiteral,\n              ),\n            }) as t.Statement,\n          ])[0] as NodePath<t.ExpressionStatement>;\n          const umdFactory = (\n            umdWrapper.get(\"expression.arguments\")[1] as NodePath<t.Function>\n          ).get(\"body\") as NodePath<t.BlockStatement>;\n          umdFactory.pushContainer(\"directives\", directives);\n          umdFactory.pushContainer(\"body\", body);\n        },\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,uBAAA,GAAAF,OAAA;AAYA,IAAAG,KAAA,GAAAH,OAAA;AAEA,MAAMI,2BAA2B,GAAG,IAAAC,cAAQ,EAAC;AAC7C;AACA,CAAC,CAAC;AAIF,MAAMC,YAAY,GAAG,IAAAD,cAAQ,EAAC;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAAC;AAAC,IAAAE,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAaY,IAAAC,0BAAO,EAAC,CAACC,GAAG,EAAEC,OAAgB,KAAK;EAAA,IAAAC,eAAA,EAAAC,gBAAA;EAChDH,GAAG,CAACI,aAAa,CAAkB,CAAE,CAAC;EAEtC,MAAM;IACJC,OAAO;IACPC,YAAY;IACZC,iBAAiB;IACjBC,MAAM;IACNC,UAAU;IACVC,SAAS;IACTC;EACF,CAAC,GAAGV,OAAO;EAEX,MAAMW,iBAAiB,IAAAV,eAAA,GACrBF,GAAG,CAACa,UAAU,CAAC,mBAAmB,CAAC,YAAAX,eAAA,GAAID,OAAO,CAACa,KAAK;EACtD,MAAMC,oBAAoB,IAAAZ,gBAAA,GACxBH,GAAG,CAACa,UAAU,CAAC,sBAAsB,CAAC,YAAAV,gBAAA,GAAIF,OAAO,CAACa,KAAK;EAKzD,SAASE,gBAAgBA,CACvBC,cAAsC,EACtCX,YAAqB,EACrBY,QAAgB,EAChBC,UAAkC,EAClC;IACA,MAAMC,oBAAoB,GAAGD,UAAU,GACnCA,UAAU,CAACE,KAAK,GAChB,IAAAC,cAAQ,EAACJ,QAAQ,EAAE,IAAAK,aAAO,EAACL,QAAQ,CAAC,CAAC;IACzC,IAAIM,cAAc,GAAGC,WAAC,CAACC,gBAAgB,CACrCD,WAAC,CAACE,UAAU,CAAC,QAAQ,CAAC,EACtBF,WAAC,CAACE,UAAU,CAACF,WAAC,CAACG,YAAY,CAACR,oBAAoB,CAAC,CACnD,CAAC;IACD,IAAIS,eAAe,GAAG,EAAE;IAExB,IAAIvB,YAAY,EAAE;MAChB,MAAMwB,UAAU,GAAGb,cAAc,CAACG,oBAAoB,CAAC;MAEvD,IAAIU,UAAU,EAAE;QACdD,eAAe,GAAG,EAAE;QAEpB,MAAME,OAAO,GAAGD,UAAU,CAACE,KAAK,CAAC,GAAG,CAAC;QACrCR,cAAc,GAAGO,OAAO,CAACE,KAAK,CAAC,CAAC,CAAC,CAACC,MAAM,CACtC,CAACC,KAAK,EAAEC,IAAI,KAAK;UACfP,eAAe,CAACQ,IAAI,CAClB5C,2BAA2B,CAAC;YAC1B6C,gBAAgB,EAAEb,WAAC,CAACc,SAAS,CAACJ,KAAK;UACrC,CAAC,CACH,CAAC;UACD,OAAOV,WAAC,CAACC,gBAAgB,CAACS,KAAK,EAAEV,WAAC,CAACE,UAAU,CAACS,IAAI,CAAC,CAAC;QACtD,CAAC,EACDX,WAAC,CAACC,gBAAgB,CAACD,WAAC,CAACE,UAAU,CAAC,QAAQ,CAAC,EAAEF,WAAC,CAACE,UAAU,CAACI,OAAO,CAAC,CAAC,CAAC,CAAC,CACrE,CAAC;MACH;IACF;IAEAF,eAAe,CAACQ,IAAI,CAClBZ,WAAC,CAACe,mBAAmB,CACnBf,WAAC,CAACgB,oBAAoB,CACpB,GAAG,EACHjB,cAAc,EACdC,WAAC,CAACC,gBAAgB,CAACD,WAAC,CAACE,UAAU,CAAC,KAAK,CAAC,EAAEF,WAAC,CAACE,UAAU,CAAC,SAAS,CAAC,CACjE,CACF,CACF,CAAC;IAED,OAAOE,eAAe;EACxB;EAKA,SAASa,eAAeA,CACtBzB,cAAsC,EACtCX,YAAqB,EACrBqC,MAAc,EACd;IACA,IAAIjB,gBAAoC;IACxC,IAAIpB,YAAY,EAAE;MAChB,MAAMsC,SAAS,GAAG3B,cAAc,CAAC0B,MAAM,CAAC;MACxC,IAAIC,SAAS,EAAE;QACblB,gBAAgB,GAAGkB,SAAS,CACzBZ,KAAK,CAAC,GAAG,CAAC,CACVE,MAAM,CACL,CAACC,KAAwC,EAAEC,IAAI,KAC7CX,WAAC,CAACC,gBAAgB,CAACS,KAAK,EAAEV,WAAC,CAACE,UAAU,CAACS,IAAI,CAAC,CAAC,EAC/CX,WAAC,CAACE,UAAU,CAAC,QAAQ,CACvB,CAAuB;MAC3B,CAAC,MAAM;QACLD,gBAAgB,GAAGD,WAAC,CAACC,gBAAgB,CACnCD,WAAC,CAACE,UAAU,CAAC,QAAQ,CAAC,EACtBF,WAAC,CAACE,UAAU,CAACF,WAAC,CAACG,YAAY,CAACe,MAAM,CAAC,CACrC,CAAC;MACH;IACF,CAAC,MAAM;MACL,MAAME,WAAW,GAAG,IAAAvB,cAAQ,EAACqB,MAAM,EAAE,IAAApB,aAAO,EAACoB,MAAM,CAAC,CAAC;MACrD,MAAMb,UAAU,GAAGb,cAAc,CAAC4B,WAAW,CAAC,IAAIA,WAAW;MAC7DnB,gBAAgB,GAAGD,WAAC,CAACC,gBAAgB,CACnCD,WAAC,CAACE,UAAU,CAAC,QAAQ,CAAC,EACtBF,WAAC,CAACE,UAAU,CAACF,WAAC,CAACG,YAAY,CAACE,UAAU,CAAC,CACzC,CAAC;IACH;IACA,OAAOJ,gBAAgB;EACzB;EAEA,OAAO;IACLoB,IAAI,EAAE,uBAAuB;IAE7BC,OAAO,EAAE;MACPC,OAAO,EAAE;QACPC,IAAIA,CAACC,IAAI,EAAE;UACT,IAAI,CAAC,IAAAC,gCAAQ,EAACD,IAAI,CAAC,EAAE;UAErB,MAAMjC,cAAc,GAAGZ,OAAO,IAAI,CAAC,CAAC;UAEpC,MAAMc,UAAU,GAAG,IAAAiC,qCAAa,EAAC,IAAI,CAACC,IAAI,CAACC,IAAI,EAAErD,OAAO,CAAC;UACzD,IAAIsD,iBAAyC;UAC7C,IAAIpC,UAAU,EAAEoC,iBAAiB,GAAG9B,WAAC,CAAC+B,aAAa,CAACrC,UAAU,CAAC;UAE/D,MAAM;YAAEsC,IAAI;YAAEC;UAAQ,CAAC,GAAG,IAAAC,+DAAuC,EAC/DT,IAAI,EACJ;YACEtC,iBAAiB;YACjBG,oBAAoB;YACpBP,MAAM;YACNC,UAAU;YACVF,iBAAiB;YACjBG,SAAS;YACTC,aAAa;YACbO,QAAQ,EAAE,IAAI,CAACmC,IAAI,CAACC,IAAI,CAACpC;UAC3B,CACF,CAAC;UAED,MAAM0C,OAAO,GAAG,EAAE;UAClB,MAAMC,YAAY,GAAG,EAAE;UACvB,MAAMC,WAAW,GAAG,EAAE;UACtB,MAAMC,WAAW,GAAG,EAAE;UAEtB,IAAI,IAAAC,kCAAU,EAACP,IAAI,CAAC,EAAE;YACpBG,OAAO,CAACvB,IAAI,CAACZ,WAAC,CAAC+B,aAAa,CAAC,SAAS,CAAC,CAAC;YACxCK,YAAY,CAACxB,IAAI,CAACZ,WAAC,CAACE,UAAU,CAAC,SAAS,CAAC,CAAC;YAC1CmC,WAAW,CAACzB,IAAI,CACdZ,WAAC,CAACC,gBAAgB,CAACD,WAAC,CAACE,UAAU,CAAC,KAAK,CAAC,EAAEF,WAAC,CAACE,UAAU,CAAC,SAAS,CAAC,CACjE,CAAC;YACDoC,WAAW,CAAC1B,IAAI,CAACZ,WAAC,CAACE,UAAU,CAAC8B,IAAI,CAACQ,UAAU,CAAC,CAAC;UACjD;UAEA,KAAK,MAAM,CAACtB,MAAM,EAAEuB,QAAQ,CAAC,IAAIT,IAAI,CAACd,MAAM,EAAE;YAC5CiB,OAAO,CAACvB,IAAI,CAACZ,WAAC,CAAC+B,aAAa,CAACb,MAAM,CAAC,CAAC;YACrCkB,YAAY,CAACxB,IAAI,CACfZ,WAAC,CAAC0C,cAAc,CAAC1C,WAAC,CAACE,UAAU,CAAC,SAAS,CAAC,EAAE,CACxCF,WAAC,CAAC+B,aAAa,CAACb,MAAM,CAAC,CACxB,CACH,CAAC;YACDmB,WAAW,CAACzB,IAAI,CACdK,eAAe,CAACzB,cAAc,EAAEX,YAAY,EAAEqC,MAAM,CACtD,CAAC;YACDoB,WAAW,CAAC1B,IAAI,CAACZ,WAAC,CAACE,UAAU,CAACuC,QAAQ,CAACpB,IAAI,CAAC,CAAC;YAE7C,IAAI,CAAC,IAAAsB,0CAAkB,EAACF,QAAQ,CAAC,EAAE;cACjC,MAAMG,OAAO,GAAG,IAAAC,mCAAW,EACzBpB,IAAI,EACJzB,WAAC,CAACE,UAAU,CAACuC,QAAQ,CAACpB,IAAI,CAAC,EAC3BoB,QAAQ,CAACG,OACX,CAAC;cACD,IAAIA,OAAO,EAAE;gBACX,MAAME,MAAM,GAAG9C,WAAC,CAACe,mBAAmB,CAClCf,WAAC,CAACgB,oBAAoB,CACpB,GAAG,EACHhB,WAAC,CAACE,UAAU,CAACuC,QAAQ,CAACpB,IAAI,CAAC,EAC3BuB,OACF,CACF,CAAC;gBAEDE,MAAM,CAACC,GAAG,GAAGf,IAAI,CAACe,GAAG;gBACrBd,OAAO,CAACrB,IAAI,CAACkC,MAAM,CAAC;cACtB;YACF;YAEAb,OAAO,CAACrB,IAAI,CACV,GAAG,IAAAoC,oDAA4B,EAC7BhB,IAAI,EACJS,QAAQ,EACRtD,iBACF,CACF,CAAC;UACH;UAEA,IAAA8D,+CAAuB,EAAChB,OAAO,CAAC;UAChCR,IAAI,CAACyB,gBAAgB,CAAC,MAAM,EAAEjB,OAAO,CAAC;UAEtC,MAAM;YAAEkB,IAAI;YAAEC;UAAW,CAAC,GAAG3B,IAAI,CAAC4B,IAAI;UACtC5B,IAAI,CAAC4B,IAAI,CAACD,UAAU,GAAG,EAAE;UACzB3B,IAAI,CAAC4B,IAAI,CAACF,IAAI,GAAG,EAAE;UACnB,MAAMG,UAAU,GAAG7B,IAAI,CAAC8B,aAAa,CAAC,MAAM,EAAE,CAC5CrF,YAAY,CAAC;YAEXsF,WAAW,EAAE1B,iBAAiB;YAE9B2B,aAAa,EAAEzD,WAAC,CAAC0D,eAAe,CAACvB,OAAO,CAAC;YACzCwB,kBAAkB,EAAEvB,YAAY;YAChCwB,iBAAiB,EAAEvB,WAAW;YAC9BwB,YAAY,EAAEvB,WAAW;YAEzBwB,gBAAgB,EAAEvE,gBAAgB,CAChCC,cAAc,EACdX,YAAY,EACZ,IAAI,CAACY,QAAQ,IAAI,SAAS,EAC1BqC,iBACF;UACF,CAAC,CAAC,CACH,CAAC,CAAC,CAAC,CAAoC;UACxC,MAAMiC,UAAU,GACdT,UAAU,CAACU,GAAG,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CACzCA,GAAG,CAAC,MAAM,CAA+B;UAC3CD,UAAU,CAACR,aAAa,CAAC,YAAY,EAAEH,UAAU,CAAC;UAClDW,UAAU,CAACR,aAAa,CAAC,MAAM,EAAEJ,IAAI,CAAC;QACxC;MACF;IACF;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}