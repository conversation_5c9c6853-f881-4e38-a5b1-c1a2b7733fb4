{"ast": null, "code": "import { sqrt } from \"../math.js\";\nexport default {\n  draw(context, size) {\n    const r = sqrt(size / 5) / 2;\n    context.moveTo(-3 * r, -r);\n    context.lineTo(-r, -r);\n    context.lineTo(-r, -3 * r);\n    context.lineTo(r, -3 * r);\n    context.lineTo(r, -r);\n    context.lineTo(3 * r, -r);\n    context.lineTo(3 * r, r);\n    context.lineTo(r, r);\n    context.lineTo(r, 3 * r);\n    context.lineTo(-r, 3 * r);\n    context.lineTo(-r, r);\n    context.lineTo(-3 * r, r);\n    context.closePath();\n  }\n};", "map": {"version": 3, "names": ["sqrt", "draw", "context", "size", "r", "moveTo", "lineTo", "closePath"], "sources": ["/Users/<USER>/Downloads/interview-final/node_modules/d3-shape/src/symbol/cross.js"], "sourcesContent": ["import {sqrt} from \"../math.js\";\n\nexport default {\n  draw(context, size) {\n    const r = sqrt(size / 5) / 2;\n    context.moveTo(-3 * r, -r);\n    context.lineTo(-r, -r);\n    context.lineTo(-r, -3 * r);\n    context.lineTo(r, -3 * r);\n    context.lineTo(r, -r);\n    context.lineTo(3 * r, -r);\n    context.lineTo(3 * r, r);\n    context.lineTo(r, r);\n    context.lineTo(r, 3 * r);\n    context.lineTo(-r, 3 * r);\n    context.lineTo(-r, r);\n    context.lineTo(-3 * r, r);\n    context.closePath();\n  }\n};\n"], "mappings": "AAAA,SAAQA,IAAI,QAAO,YAAY;AAE/B,eAAe;EACbC,IAAIA,CAACC,OAAO,EAAEC,IAAI,EAAE;IAClB,MAAMC,CAAC,GAAGJ,IAAI,CAACG,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC;IAC5BD,OAAO,CAACG,MAAM,CAAC,CAAC,CAAC,GAAGD,CAAC,EAAE,CAACA,CAAC,CAAC;IAC1BF,OAAO,CAACI,MAAM,CAAC,CAACF,CAAC,EAAE,CAACA,CAAC,CAAC;IACtBF,OAAO,CAACI,MAAM,CAAC,CAACF,CAAC,EAAE,CAAC,CAAC,GAAGA,CAAC,CAAC;IAC1BF,OAAO,CAACI,MAAM,CAACF,CAAC,EAAE,CAAC,CAAC,GAAGA,CAAC,CAAC;IACzBF,OAAO,CAACI,MAAM,CAACF,CAAC,EAAE,CAACA,CAAC,CAAC;IACrBF,OAAO,CAACI,MAAM,CAAC,CAAC,GAAGF,CAAC,EAAE,CAACA,CAAC,CAAC;IACzBF,OAAO,CAACI,MAAM,CAAC,CAAC,GAAGF,CAAC,EAAEA,CAAC,CAAC;IACxBF,OAAO,CAACI,MAAM,CAACF,CAAC,EAAEA,CAAC,CAAC;IACpBF,OAAO,CAACI,MAAM,CAACF,CAAC,EAAE,CAAC,GAAGA,CAAC,CAAC;IACxBF,OAAO,CAACI,MAAM,CAAC,CAACF,CAAC,EAAE,CAAC,GAAGA,CAAC,CAAC;IACzBF,OAAO,CAACI,MAAM,CAAC,CAACF,CAAC,EAAEA,CAAC,CAAC;IACrBF,OAAO,CAACI,MAAM,CAAC,CAAC,CAAC,GAAGF,CAAC,EAAEA,CAAC,CAAC;IACzBF,OAAO,CAACK,SAAS,CAAC,CAAC;EACrB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}