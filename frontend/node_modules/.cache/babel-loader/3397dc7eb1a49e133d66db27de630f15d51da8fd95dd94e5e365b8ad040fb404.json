{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Downloads/interview-final/src/App.js\";\nimport React from \"react\";\nimport AIInterview from \"./ai_interviewer_webapp\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function App() {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: /*#__PURE__*/_jsxDEV(AIInterview, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "AIInterview", "jsxDEV", "_jsxDEV", "App", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Downloads/interview-final/src/App.js"], "sourcesContent": ["import React from \"react\";\nimport AIInterview from \"./ai_interviewer_webapp\";\n\nexport default function App() {\n  return (\n    <div>\n      <AIInterview />\n    </div>\n  );\n}\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,WAAW,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,eAAe,SAASC,GAAGA,CAAA,EAAG;EAC5B,oBACED,OAAA;IAAAE,QAAA,eACEF,OAAA,CAACF,WAAW;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEV;AAACC,EAAA,GANuBN,GAAG;AAAA,IAAAM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}