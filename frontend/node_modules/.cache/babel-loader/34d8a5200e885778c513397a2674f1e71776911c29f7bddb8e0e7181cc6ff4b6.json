{"ast": null, "code": "import uniqBy from 'lodash/uniqBy';\nimport isFunction from 'lodash/isFunction';\n\n/**\n * This is configuration option that decides how to filter for unique values only:\n *\n * - `false` means \"no filter\"\n * - `true` means \"use recharts default filter\"\n * - function means \"use return of this function as the default key\"\n */\n\nexport function getUniqPayload(payload, option, defaultUniqBy) {\n  if (option === true) {\n    return uniqBy(payload, defaultUniqBy);\n  }\n  if (isFunction(option)) {\n    return uniqBy(payload, option);\n  }\n  return payload;\n}", "map": {"version": 3, "names": ["uniqBy", "isFunction", "getUniqPayload", "payload", "option", "defaultUniqBy"], "sources": ["/Users/<USER>/Downloads/interview-final/frontend/node_modules/recharts/es6/util/payload/getUniqPayload.js"], "sourcesContent": ["import uniqBy from 'lodash/uniqBy';\nimport isFunction from 'lodash/isFunction';\n\n/**\n * This is configuration option that decides how to filter for unique values only:\n *\n * - `false` means \"no filter\"\n * - `true` means \"use recharts default filter\"\n * - function means \"use return of this function as the default key\"\n */\n\nexport function getUniqPayload(payload, option, defaultUniqBy) {\n  if (option === true) {\n    return uniqBy(payload, defaultUniqBy);\n  }\n  if (isFunction(option)) {\n    return uniqBy(payload, option);\n  }\n  return payload;\n}"], "mappings": "AAAA,OAAOA,MAAM,MAAM,eAAe;AAClC,OAAOC,UAAU,MAAM,mBAAmB;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASC,cAAcA,CAACC,OAAO,EAAEC,MAAM,EAAEC,aAAa,EAAE;EAC7D,IAAID,MAAM,KAAK,IAAI,EAAE;IACnB,OAAOJ,MAAM,CAACG,OAAO,EAAEE,aAAa,CAAC;EACvC;EACA,IAAIJ,UAAU,CAACG,MAAM,CAAC,EAAE;IACtB,OAAOJ,MAAM,CAACG,OAAO,EAAEC,MAAM,CAAC;EAChC;EACA,OAAOD,OAAO;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}