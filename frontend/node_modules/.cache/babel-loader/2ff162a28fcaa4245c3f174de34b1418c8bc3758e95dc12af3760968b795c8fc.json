{"ast": null, "code": "var eq = require('./eq'),\n  isArrayLike = require('./isArrayLike'),\n  isIndex = require('./_isIndex'),\n  isObject = require('./isObject');\n\n/**\n * Checks if the given arguments are from an iteratee call.\n *\n * @private\n * @param {*} value The potential iteratee value argument.\n * @param {*} index The potential iteratee index or key argument.\n * @param {*} object The potential iteratee object argument.\n * @returns {boolean} Returns `true` if the arguments are from an iteratee call,\n *  else `false`.\n */\nfunction isIterateeCall(value, index, object) {\n  if (!isObject(object)) {\n    return false;\n  }\n  var type = typeof index;\n  if (type == 'number' ? isArrayLike(object) && isIndex(index, object.length) : type == 'string' && index in object) {\n    return eq(object[index], value);\n  }\n  return false;\n}\nmodule.exports = isIterateeCall;", "map": {"version": 3, "names": ["eq", "require", "isArrayLike", "isIndex", "isObject", "isIterateeCall", "value", "index", "object", "type", "length", "module", "exports"], "sources": ["/Users/<USER>/Downloads/interview-final/node_modules/lodash/_isIterateeCall.js"], "sourcesContent": ["var eq = require('./eq'),\n    isArrayLike = require('./isArrayLike'),\n    isIndex = require('./_isIndex'),\n    isObject = require('./isObject');\n\n/**\n * Checks if the given arguments are from an iteratee call.\n *\n * @private\n * @param {*} value The potential iteratee value argument.\n * @param {*} index The potential iteratee index or key argument.\n * @param {*} object The potential iteratee object argument.\n * @returns {boolean} Returns `true` if the arguments are from an iteratee call,\n *  else `false`.\n */\nfunction isIterateeCall(value, index, object) {\n  if (!isObject(object)) {\n    return false;\n  }\n  var type = typeof index;\n  if (type == 'number'\n        ? (isArrayLike(object) && isIndex(index, object.length))\n        : (type == 'string' && index in object)\n      ) {\n    return eq(object[index], value);\n  }\n  return false;\n}\n\nmodule.exports = isIterateeCall;\n"], "mappings": "AAAA,IAAIA,EAAE,GAAGC,OAAO,CAAC,MAAM,CAAC;EACpBC,WAAW,GAAGD,OAAO,CAAC,eAAe,CAAC;EACtCE,OAAO,GAAGF,OAAO,CAAC,YAAY,CAAC;EAC/BG,QAAQ,GAAGH,OAAO,CAAC,YAAY,CAAC;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,MAAM,EAAE;EAC5C,IAAI,CAACJ,QAAQ,CAACI,MAAM,CAAC,EAAE;IACrB,OAAO,KAAK;EACd;EACA,IAAIC,IAAI,GAAG,OAAOF,KAAK;EACvB,IAAIE,IAAI,IAAI,QAAQ,GACXP,WAAW,CAACM,MAAM,CAAC,IAAIL,OAAO,CAACI,KAAK,EAAEC,MAAM,CAACE,MAAM,CAAC,GACpDD,IAAI,IAAI,QAAQ,IAAIF,KAAK,IAAIC,MAAO,EACvC;IACJ,OAAOR,EAAE,CAACQ,MAAM,CAACD,KAAK,CAAC,EAAED,KAAK,CAAC;EACjC;EACA,OAAO,KAAK;AACd;AAEAK,MAAM,CAACC,OAAO,GAAGP,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}