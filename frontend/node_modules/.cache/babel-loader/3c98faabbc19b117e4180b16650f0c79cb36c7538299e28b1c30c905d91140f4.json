{"ast": null, "code": "var baseIteratee = require('./_baseIteratee'),\n  isArrayLike = require('./isArrayLike'),\n  keys = require('./keys');\n\n/**\n * Creates a `_.find` or `_.findLast` function.\n *\n * @private\n * @param {Function} findIndexFunc The function to find the collection index.\n * @returns {Function} Returns the new find function.\n */\nfunction createFind(findIndexFunc) {\n  return function (collection, predicate, fromIndex) {\n    var iterable = Object(collection);\n    if (!isArrayLike(collection)) {\n      var iteratee = baseIteratee(predicate, 3);\n      collection = keys(collection);\n      predicate = function (key) {\n        return iteratee(iterable[key], key, iterable);\n      };\n    }\n    var index = findIndexFunc(collection, predicate, fromIndex);\n    return index > -1 ? iterable[iteratee ? collection[index] : index] : undefined;\n  };\n}\nmodule.exports = createFind;", "map": {"version": 3, "names": ["baseIteratee", "require", "isArrayLike", "keys", "createFind", "findIndexFunc", "collection", "predicate", "fromIndex", "iterable", "Object", "iteratee", "key", "index", "undefined", "module", "exports"], "sources": ["/Users/<USER>/Downloads/interview-final/frontend/node_modules/lodash/_createFind.js"], "sourcesContent": ["var baseIteratee = require('./_baseIteratee'),\n    isArrayLike = require('./isArrayLike'),\n    keys = require('./keys');\n\n/**\n * Creates a `_.find` or `_.findLast` function.\n *\n * @private\n * @param {Function} findIndexFunc The function to find the collection index.\n * @returns {Function} Returns the new find function.\n */\nfunction createFind(findIndexFunc) {\n  return function(collection, predicate, fromIndex) {\n    var iterable = Object(collection);\n    if (!isArrayLike(collection)) {\n      var iteratee = baseIteratee(predicate, 3);\n      collection = keys(collection);\n      predicate = function(key) { return iteratee(iterable[key], key, iterable); };\n    }\n    var index = findIndexFunc(collection, predicate, fromIndex);\n    return index > -1 ? iterable[iteratee ? collection[index] : index] : undefined;\n  };\n}\n\nmodule.exports = createFind;\n"], "mappings": "AAAA,IAAIA,YAAY,GAAGC,OAAO,CAAC,iBAAiB,CAAC;EACzCC,WAAW,GAAGD,OAAO,CAAC,eAAe,CAAC;EACtCE,IAAI,GAAGF,OAAO,CAAC,QAAQ,CAAC;;AAE5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,UAAUA,CAACC,aAAa,EAAE;EACjC,OAAO,UAASC,UAAU,EAAEC,SAAS,EAAEC,SAAS,EAAE;IAChD,IAAIC,QAAQ,GAAGC,MAAM,CAACJ,UAAU,CAAC;IACjC,IAAI,CAACJ,WAAW,CAACI,UAAU,CAAC,EAAE;MAC5B,IAAIK,QAAQ,GAAGX,YAAY,CAACO,SAAS,EAAE,CAAC,CAAC;MACzCD,UAAU,GAAGH,IAAI,CAACG,UAAU,CAAC;MAC7BC,SAAS,GAAG,SAAAA,CAASK,GAAG,EAAE;QAAE,OAAOD,QAAQ,CAACF,QAAQ,CAACG,GAAG,CAAC,EAAEA,GAAG,EAAEH,QAAQ,CAAC;MAAE,CAAC;IAC9E;IACA,IAAII,KAAK,GAAGR,aAAa,CAACC,UAAU,EAAEC,SAAS,EAAEC,SAAS,CAAC;IAC3D,OAAOK,KAAK,GAAG,CAAC,CAAC,GAAGJ,QAAQ,CAACE,QAAQ,GAAGL,UAAU,CAACO,KAAK,CAAC,GAAGA,KAAK,CAAC,GAAGC,SAAS;EAChF,CAAC;AACH;AAEAC,MAAM,CAACC,OAAO,GAAGZ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}