{"ast": null, "code": "var baseIsSet = require('./_baseIsSet'),\n  baseUnary = require('./_baseUnary'),\n  nodeUtil = require('./_nodeUtil');\n\n/* Node.js helper references. */\nvar nodeIsSet = nodeUtil && nodeUtil.isSet;\n\n/**\n * Checks if `value` is classified as a `Set` object.\n *\n * @static\n * @memberOf _\n * @since 4.3.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a set, else `false`.\n * @example\n *\n * _.isSet(new Set);\n * // => true\n *\n * _.isSet(new WeakSet);\n * // => false\n */\nvar isSet = nodeIsSet ? baseUnary(nodeIsSet) : baseIsSet;\nmodule.exports = isSet;", "map": {"version": 3, "names": ["baseIsSet", "require", "baseUnary", "nodeUtil", "nodeIsSet", "isSet", "module", "exports"], "sources": ["/Users/<USER>/Downloads/interview-final/node_modules/lodash/isSet.js"], "sourcesContent": ["var baseIsSet = require('./_baseIsSet'),\n    baseUnary = require('./_baseUnary'),\n    nodeUtil = require('./_nodeUtil');\n\n/* Node.js helper references. */\nvar nodeIsSet = nodeUtil && nodeUtil.isSet;\n\n/**\n * Checks if `value` is classified as a `Set` object.\n *\n * @static\n * @memberOf _\n * @since 4.3.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a set, else `false`.\n * @example\n *\n * _.isSet(new Set);\n * // => true\n *\n * _.isSet(new WeakSet);\n * // => false\n */\nvar isSet = nodeIsSet ? baseUnary(nodeIsSet) : baseIsSet;\n\nmodule.exports = isSet;\n"], "mappings": "AAAA,IAAIA,SAAS,GAAGC,OAAO,CAAC,cAAc,CAAC;EACnCC,SAAS,GAAGD,OAAO,CAAC,cAAc,CAAC;EACnCE,QAAQ,GAAGF,OAAO,CAAC,aAAa,CAAC;;AAErC;AACA,IAAIG,SAAS,GAAGD,QAAQ,IAAIA,QAAQ,CAACE,KAAK;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,KAAK,GAAGD,SAAS,GAAGF,SAAS,CAACE,SAAS,CAAC,GAAGJ,SAAS;AAExDM,MAAM,CAACC,OAAO,GAAGF,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}