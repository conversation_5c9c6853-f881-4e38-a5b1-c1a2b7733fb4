{"ast": null, "code": "function _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nimport isFunction from 'lodash/isFunction';\nimport { mathSign, isNumber } from '../util/DataUtils';\nimport { getStringSize } from '../util/DOMUtils';\nimport { Global } from '../util/Global';\nimport { isVisible, getTickBoundaries, getNumberIntervalTicks, getAngledTickWidth } from '../util/TickUtils';\nimport { getEquidistantTicks } from './getEquidistantTicks';\nfunction getTicksEnd(sign, boundaries, getTickSize, ticks, minTickGap) {\n  var result = (ticks || []).slice();\n  var len = result.length;\n  var start = boundaries.start;\n  var end = boundaries.end;\n  var _loop = function _loop(i) {\n    var entry = result[i];\n    var size;\n    var getSize = function getSize() {\n      if (size === undefined) {\n        size = getTickSize(entry, i);\n      }\n      return size;\n    };\n    if (i === len - 1) {\n      var gap = sign * (entry.coordinate + sign * getSize() / 2 - end);\n      result[i] = entry = _objectSpread(_objectSpread({}, entry), {}, {\n        tickCoord: gap > 0 ? entry.coordinate - gap * sign : entry.coordinate\n      });\n    } else {\n      result[i] = entry = _objectSpread(_objectSpread({}, entry), {}, {\n        tickCoord: entry.coordinate\n      });\n    }\n    var isShow = isVisible(sign, entry.tickCoord, getSize, start, end);\n    if (isShow) {\n      end = entry.tickCoord - sign * (getSize() / 2 + minTickGap);\n      result[i] = _objectSpread(_objectSpread({}, entry), {}, {\n        isShow: true\n      });\n    }\n  };\n  for (var i = len - 1; i >= 0; i--) {\n    _loop(i);\n  }\n  return result;\n}\nfunction getTicksStart(sign, boundaries, getTickSize, ticks, minTickGap, preserveEnd) {\n  var result = (ticks || []).slice();\n  var len = result.length;\n  var start = boundaries.start,\n    end = boundaries.end;\n  if (preserveEnd) {\n    // Try to guarantee the tail to be displayed\n    var tail = ticks[len - 1];\n    var tailSize = getTickSize(tail, len - 1);\n    var tailGap = sign * (tail.coordinate + sign * tailSize / 2 - end);\n    result[len - 1] = tail = _objectSpread(_objectSpread({}, tail), {}, {\n      tickCoord: tailGap > 0 ? tail.coordinate - tailGap * sign : tail.coordinate\n    });\n    var isTailShow = isVisible(sign, tail.tickCoord, function () {\n      return tailSize;\n    }, start, end);\n    if (isTailShow) {\n      end = tail.tickCoord - sign * (tailSize / 2 + minTickGap);\n      result[len - 1] = _objectSpread(_objectSpread({}, tail), {}, {\n        isShow: true\n      });\n    }\n  }\n  var count = preserveEnd ? len - 1 : len;\n  var _loop2 = function _loop2(i) {\n    var entry = result[i];\n    var size;\n    var getSize = function getSize() {\n      if (size === undefined) {\n        size = getTickSize(entry, i);\n      }\n      return size;\n    };\n    if (i === 0) {\n      var gap = sign * (entry.coordinate - sign * getSize() / 2 - start);\n      result[i] = entry = _objectSpread(_objectSpread({}, entry), {}, {\n        tickCoord: gap < 0 ? entry.coordinate - gap * sign : entry.coordinate\n      });\n    } else {\n      result[i] = entry = _objectSpread(_objectSpread({}, entry), {}, {\n        tickCoord: entry.coordinate\n      });\n    }\n    var isShow = isVisible(sign, entry.tickCoord, getSize, start, end);\n    if (isShow) {\n      start = entry.tickCoord + sign * (getSize() / 2 + minTickGap);\n      result[i] = _objectSpread(_objectSpread({}, entry), {}, {\n        isShow: true\n      });\n    }\n  };\n  for (var i = 0; i < count; i++) {\n    _loop2(i);\n  }\n  return result;\n}\nexport function getTicks(props, fontSize, letterSpacing) {\n  var tick = props.tick,\n    ticks = props.ticks,\n    viewBox = props.viewBox,\n    minTickGap = props.minTickGap,\n    orientation = props.orientation,\n    interval = props.interval,\n    tickFormatter = props.tickFormatter,\n    unit = props.unit,\n    angle = props.angle;\n  if (!ticks || !ticks.length || !tick) {\n    return [];\n  }\n  if (isNumber(interval) || Global.isSsr) {\n    return getNumberIntervalTicks(ticks, typeof interval === 'number' && isNumber(interval) ? interval : 0);\n  }\n  var candidates = [];\n  var sizeKey = orientation === 'top' || orientation === 'bottom' ? 'width' : 'height';\n  var unitSize = unit && sizeKey === 'width' ? getStringSize(unit, {\n    fontSize: fontSize,\n    letterSpacing: letterSpacing\n  }) : {\n    width: 0,\n    height: 0\n  };\n  var getTickSize = function getTickSize(content, index) {\n    var value = isFunction(tickFormatter) ? tickFormatter(content.value, index) : content.value;\n    // Recharts only supports angles when sizeKey === 'width'\n    return sizeKey === 'width' ? getAngledTickWidth(getStringSize(value, {\n      fontSize: fontSize,\n      letterSpacing: letterSpacing\n    }), unitSize, angle) : getStringSize(value, {\n      fontSize: fontSize,\n      letterSpacing: letterSpacing\n    })[sizeKey];\n  };\n  var sign = ticks.length >= 2 ? mathSign(ticks[1].coordinate - ticks[0].coordinate) : 1;\n  var boundaries = getTickBoundaries(viewBox, sign, sizeKey);\n  if (interval === 'equidistantPreserveStart') {\n    return getEquidistantTicks(sign, boundaries, getTickSize, ticks, minTickGap);\n  }\n  if (interval === 'preserveStart' || interval === 'preserveStartEnd') {\n    candidates = getTicksStart(sign, boundaries, getTickSize, ticks, minTickGap, interval === 'preserveStartEnd');\n  } else {\n    candidates = getTicksEnd(sign, boundaries, getTickSize, ticks, minTickGap);\n  }\n  return candidates.filter(function (entry) {\n    return entry.isShow;\n  });\n}", "map": {"version": 3, "names": ["_typeof", "o", "Symbol", "iterator", "constructor", "prototype", "ownKeys", "e", "r", "t", "Object", "keys", "getOwnPropertySymbols", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "length", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "obj", "key", "value", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "configurable", "writable", "i", "_toPrimitive", "toPrimitive", "call", "TypeError", "String", "Number", "isFunction", "mathSign", "isNumber", "getStringSize", "Global", "isVisible", "getTickBoundaries", "getNumberIntervalTicks", "getAngledTickWidth", "getEquidistantTicks", "getTicksEnd", "sign", "boundaries", "getTickSize", "ticks", "minTickGap", "result", "slice", "len", "start", "end", "_loop", "entry", "size", "getSize", "undefined", "gap", "coordinate", "tickCoord", "isShow", "getTicksStart", "preserveEnd", "tail", "tailSize", "tailGap", "isTailShow", "count", "_loop2", "getTicks", "props", "fontSize", "letterSpacing", "tick", "viewBox", "orientation", "interval", "tick<PERSON><PERSON><PERSON><PERSON>", "unit", "angle", "isSsr", "candidates", "sizeKey", "unitSize", "width", "height", "content", "index"], "sources": ["/Users/<USER>/Downloads/interview-final/frontend/node_modules/recharts/es6/cartesian/getTicks.js"], "sourcesContent": ["function _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport isFunction from 'lodash/isFunction';\nimport { mathSign, isNumber } from '../util/DataUtils';\nimport { getStringSize } from '../util/DOMUtils';\nimport { Global } from '../util/Global';\nimport { isVisible, getTickBoundaries, getNumberIntervalTicks, getAngledTickWidth } from '../util/TickUtils';\nimport { getEquidistantTicks } from './getEquidistantTicks';\nfunction getTicksEnd(sign, boundaries, getTickSize, ticks, minTickGap) {\n  var result = (ticks || []).slice();\n  var len = result.length;\n  var start = boundaries.start;\n  var end = boundaries.end;\n  var _loop = function _loop(i) {\n    var entry = result[i];\n    var size;\n    var getSize = function getSize() {\n      if (size === undefined) {\n        size = getTickSize(entry, i);\n      }\n      return size;\n    };\n    if (i === len - 1) {\n      var gap = sign * (entry.coordinate + sign * getSize() / 2 - end);\n      result[i] = entry = _objectSpread(_objectSpread({}, entry), {}, {\n        tickCoord: gap > 0 ? entry.coordinate - gap * sign : entry.coordinate\n      });\n    } else {\n      result[i] = entry = _objectSpread(_objectSpread({}, entry), {}, {\n        tickCoord: entry.coordinate\n      });\n    }\n    var isShow = isVisible(sign, entry.tickCoord, getSize, start, end);\n    if (isShow) {\n      end = entry.tickCoord - sign * (getSize() / 2 + minTickGap);\n      result[i] = _objectSpread(_objectSpread({}, entry), {}, {\n        isShow: true\n      });\n    }\n  };\n  for (var i = len - 1; i >= 0; i--) {\n    _loop(i);\n  }\n  return result;\n}\nfunction getTicksStart(sign, boundaries, getTickSize, ticks, minTickGap, preserveEnd) {\n  var result = (ticks || []).slice();\n  var len = result.length;\n  var start = boundaries.start,\n    end = boundaries.end;\n  if (preserveEnd) {\n    // Try to guarantee the tail to be displayed\n    var tail = ticks[len - 1];\n    var tailSize = getTickSize(tail, len - 1);\n    var tailGap = sign * (tail.coordinate + sign * tailSize / 2 - end);\n    result[len - 1] = tail = _objectSpread(_objectSpread({}, tail), {}, {\n      tickCoord: tailGap > 0 ? tail.coordinate - tailGap * sign : tail.coordinate\n    });\n    var isTailShow = isVisible(sign, tail.tickCoord, function () {\n      return tailSize;\n    }, start, end);\n    if (isTailShow) {\n      end = tail.tickCoord - sign * (tailSize / 2 + minTickGap);\n      result[len - 1] = _objectSpread(_objectSpread({}, tail), {}, {\n        isShow: true\n      });\n    }\n  }\n  var count = preserveEnd ? len - 1 : len;\n  var _loop2 = function _loop2(i) {\n    var entry = result[i];\n    var size;\n    var getSize = function getSize() {\n      if (size === undefined) {\n        size = getTickSize(entry, i);\n      }\n      return size;\n    };\n    if (i === 0) {\n      var gap = sign * (entry.coordinate - sign * getSize() / 2 - start);\n      result[i] = entry = _objectSpread(_objectSpread({}, entry), {}, {\n        tickCoord: gap < 0 ? entry.coordinate - gap * sign : entry.coordinate\n      });\n    } else {\n      result[i] = entry = _objectSpread(_objectSpread({}, entry), {}, {\n        tickCoord: entry.coordinate\n      });\n    }\n    var isShow = isVisible(sign, entry.tickCoord, getSize, start, end);\n    if (isShow) {\n      start = entry.tickCoord + sign * (getSize() / 2 + minTickGap);\n      result[i] = _objectSpread(_objectSpread({}, entry), {}, {\n        isShow: true\n      });\n    }\n  };\n  for (var i = 0; i < count; i++) {\n    _loop2(i);\n  }\n  return result;\n}\nexport function getTicks(props, fontSize, letterSpacing) {\n  var tick = props.tick,\n    ticks = props.ticks,\n    viewBox = props.viewBox,\n    minTickGap = props.minTickGap,\n    orientation = props.orientation,\n    interval = props.interval,\n    tickFormatter = props.tickFormatter,\n    unit = props.unit,\n    angle = props.angle;\n  if (!ticks || !ticks.length || !tick) {\n    return [];\n  }\n  if (isNumber(interval) || Global.isSsr) {\n    return getNumberIntervalTicks(ticks, typeof interval === 'number' && isNumber(interval) ? interval : 0);\n  }\n  var candidates = [];\n  var sizeKey = orientation === 'top' || orientation === 'bottom' ? 'width' : 'height';\n  var unitSize = unit && sizeKey === 'width' ? getStringSize(unit, {\n    fontSize: fontSize,\n    letterSpacing: letterSpacing\n  }) : {\n    width: 0,\n    height: 0\n  };\n  var getTickSize = function getTickSize(content, index) {\n    var value = isFunction(tickFormatter) ? tickFormatter(content.value, index) : content.value;\n    // Recharts only supports angles when sizeKey === 'width'\n    return sizeKey === 'width' ? getAngledTickWidth(getStringSize(value, {\n      fontSize: fontSize,\n      letterSpacing: letterSpacing\n    }), unitSize, angle) : getStringSize(value, {\n      fontSize: fontSize,\n      letterSpacing: letterSpacing\n    })[sizeKey];\n  };\n  var sign = ticks.length >= 2 ? mathSign(ticks[1].coordinate - ticks[0].coordinate) : 1;\n  var boundaries = getTickBoundaries(viewBox, sign, sizeKey);\n  if (interval === 'equidistantPreserveStart') {\n    return getEquidistantTicks(sign, boundaries, getTickSize, ticks, minTickGap);\n  }\n  if (interval === 'preserveStart' || interval === 'preserveStartEnd') {\n    candidates = getTicksStart(sign, boundaries, getTickSize, ticks, minTickGap, interval === 'preserveStartEnd');\n  } else {\n    candidates = getTicksEnd(sign, boundaries, getTickSize, ticks, minTickGap);\n  }\n  return candidates.filter(function (entry) {\n    return entry.isShow;\n  });\n}"], "mappings": "AAAA,SAASA,OAAOA,CAACC,CAAC,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,CAAC,EAAE;IAAE,OAAO,OAAOA,CAAC;EAAE,CAAC,GAAG,UAAUA,CAAC,EAAE;IAAE,OAAOA,CAAC,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,CAAC,CAACG,WAAW,KAAKF,MAAM,IAAID,CAAC,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,CAAC;EAAE,CAAC,EAAED,OAAO,CAACC,CAAC,CAAC;AAAE;AAC7T,SAASK,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAIC,CAAC,GAAGC,MAAM,CAACC,IAAI,CAACJ,CAAC,CAAC;EAAE,IAAIG,MAAM,CAACE,qBAAqB,EAAE;IAAE,IAAIX,CAAC,GAAGS,MAAM,CAACE,qBAAqB,CAACL,CAAC,CAAC;IAAEC,CAAC,KAAKP,CAAC,GAAGA,CAAC,CAACY,MAAM,CAAC,UAAUL,CAAC,EAAE;MAAE,OAAOE,MAAM,CAACI,wBAAwB,CAACP,CAAC,EAAEC,CAAC,CAAC,CAACO,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEN,CAAC,CAACO,IAAI,CAACC,KAAK,CAACR,CAAC,EAAER,CAAC,CAAC;EAAE;EAAE,OAAOQ,CAAC;AAAE;AAC9P,SAASS,aAAaA,CAACX,CAAC,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGW,SAAS,CAACC,MAAM,EAAEZ,CAAC,EAAE,EAAE;IAAE,IAAIC,CAAC,GAAG,IAAI,IAAIU,SAAS,CAACX,CAAC,CAAC,GAAGW,SAAS,CAACX,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGF,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACY,OAAO,CAAC,UAAUb,CAAC,EAAE;MAAEc,eAAe,CAACf,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAACD,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGE,MAAM,CAACa,yBAAyB,GAAGb,MAAM,CAACc,gBAAgB,CAACjB,CAAC,EAAEG,MAAM,CAACa,yBAAyB,CAACd,CAAC,CAAC,CAAC,GAAGH,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,CAAC,CAACY,OAAO,CAAC,UAAUb,CAAC,EAAE;MAAEE,MAAM,CAACe,cAAc,CAAClB,CAAC,EAAEC,CAAC,EAAEE,MAAM,CAACI,wBAAwB,CAACL,CAAC,EAAED,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AACtb,SAASe,eAAeA,CAACI,GAAG,EAAEC,GAAG,EAAEC,KAAK,EAAE;EAAED,GAAG,GAAGE,cAAc,CAACF,GAAG,CAAC;EAAE,IAAIA,GAAG,IAAID,GAAG,EAAE;IAAEhB,MAAM,CAACe,cAAc,CAACC,GAAG,EAAEC,GAAG,EAAE;MAAEC,KAAK,EAAEA,KAAK;MAAEb,UAAU,EAAE,IAAI;MAAEe,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAEL,GAAG,CAACC,GAAG,CAAC,GAAGC,KAAK;EAAE;EAAE,OAAOF,GAAG;AAAE;AAC3O,SAASG,cAAcA,CAACpB,CAAC,EAAE;EAAE,IAAIuB,CAAC,GAAGC,YAAY,CAACxB,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAIT,OAAO,CAACgC,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC5G,SAASC,YAAYA,CAACxB,CAAC,EAAED,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAIR,OAAO,CAACS,CAAC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIF,CAAC,GAAGE,CAAC,CAACP,MAAM,CAACgC,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAK3B,CAAC,EAAE;IAAE,IAAIyB,CAAC,GAAGzB,CAAC,CAAC4B,IAAI,CAAC1B,CAAC,EAAED,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAIR,OAAO,CAACgC,CAAC,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAII,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAK5B,CAAC,GAAG6B,MAAM,GAAGC,MAAM,EAAE7B,CAAC,CAAC;AAAE;AAC3T,OAAO8B,UAAU,MAAM,mBAAmB;AAC1C,SAASC,QAAQ,EAAEC,QAAQ,QAAQ,mBAAmB;AACtD,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,MAAM,QAAQ,gBAAgB;AACvC,SAASC,SAAS,EAAEC,iBAAiB,EAAEC,sBAAsB,EAAEC,kBAAkB,QAAQ,mBAAmB;AAC5G,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,SAASC,WAAWA,CAACC,IAAI,EAAEC,UAAU,EAAEC,WAAW,EAAEC,KAAK,EAAEC,UAAU,EAAE;EACrE,IAAIC,MAAM,GAAG,CAACF,KAAK,IAAI,EAAE,EAAEG,KAAK,CAAC,CAAC;EAClC,IAAIC,GAAG,GAAGF,MAAM,CAACnC,MAAM;EACvB,IAAIsC,KAAK,GAAGP,UAAU,CAACO,KAAK;EAC5B,IAAIC,GAAG,GAAGR,UAAU,CAACQ,GAAG;EACxB,IAAIC,KAAK,GAAG,SAASA,KAAKA,CAAC5B,CAAC,EAAE;IAC5B,IAAI6B,KAAK,GAAGN,MAAM,CAACvB,CAAC,CAAC;IACrB,IAAI8B,IAAI;IACR,IAAIC,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAG;MAC/B,IAAID,IAAI,KAAKE,SAAS,EAAE;QACtBF,IAAI,GAAGV,WAAW,CAACS,KAAK,EAAE7B,CAAC,CAAC;MAC9B;MACA,OAAO8B,IAAI;IACb,CAAC;IACD,IAAI9B,CAAC,KAAKyB,GAAG,GAAG,CAAC,EAAE;MACjB,IAAIQ,GAAG,GAAGf,IAAI,IAAIW,KAAK,CAACK,UAAU,GAAGhB,IAAI,GAAGa,OAAO,CAAC,CAAC,GAAG,CAAC,GAAGJ,GAAG,CAAC;MAChEJ,MAAM,CAACvB,CAAC,CAAC,GAAG6B,KAAK,GAAG3C,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE2C,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QAC9DM,SAAS,EAAEF,GAAG,GAAG,CAAC,GAAGJ,KAAK,CAACK,UAAU,GAAGD,GAAG,GAAGf,IAAI,GAAGW,KAAK,CAACK;MAC7D,CAAC,CAAC;IACJ,CAAC,MAAM;MACLX,MAAM,CAACvB,CAAC,CAAC,GAAG6B,KAAK,GAAG3C,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE2C,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QAC9DM,SAAS,EAAEN,KAAK,CAACK;MACnB,CAAC,CAAC;IACJ;IACA,IAAIE,MAAM,GAAGxB,SAAS,CAACM,IAAI,EAAEW,KAAK,CAACM,SAAS,EAAEJ,OAAO,EAAEL,KAAK,EAAEC,GAAG,CAAC;IAClE,IAAIS,MAAM,EAAE;MACVT,GAAG,GAAGE,KAAK,CAACM,SAAS,GAAGjB,IAAI,IAAIa,OAAO,CAAC,CAAC,GAAG,CAAC,GAAGT,UAAU,CAAC;MAC3DC,MAAM,CAACvB,CAAC,CAAC,GAAGd,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE2C,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QACtDO,MAAM,EAAE;MACV,CAAC,CAAC;IACJ;EACF,CAAC;EACD,KAAK,IAAIpC,CAAC,GAAGyB,GAAG,GAAG,CAAC,EAAEzB,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IACjC4B,KAAK,CAAC5B,CAAC,CAAC;EACV;EACA,OAAOuB,MAAM;AACf;AACA,SAASc,aAAaA,CAACnB,IAAI,EAAEC,UAAU,EAAEC,WAAW,EAAEC,KAAK,EAAEC,UAAU,EAAEgB,WAAW,EAAE;EACpF,IAAIf,MAAM,GAAG,CAACF,KAAK,IAAI,EAAE,EAAEG,KAAK,CAAC,CAAC;EAClC,IAAIC,GAAG,GAAGF,MAAM,CAACnC,MAAM;EACvB,IAAIsC,KAAK,GAAGP,UAAU,CAACO,KAAK;IAC1BC,GAAG,GAAGR,UAAU,CAACQ,GAAG;EACtB,IAAIW,WAAW,EAAE;IACf;IACA,IAAIC,IAAI,GAAGlB,KAAK,CAACI,GAAG,GAAG,CAAC,CAAC;IACzB,IAAIe,QAAQ,GAAGpB,WAAW,CAACmB,IAAI,EAAEd,GAAG,GAAG,CAAC,CAAC;IACzC,IAAIgB,OAAO,GAAGvB,IAAI,IAAIqB,IAAI,CAACL,UAAU,GAAGhB,IAAI,GAAGsB,QAAQ,GAAG,CAAC,GAAGb,GAAG,CAAC;IAClEJ,MAAM,CAACE,GAAG,GAAG,CAAC,CAAC,GAAGc,IAAI,GAAGrD,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEqD,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;MAClEJ,SAAS,EAAEM,OAAO,GAAG,CAAC,GAAGF,IAAI,CAACL,UAAU,GAAGO,OAAO,GAAGvB,IAAI,GAAGqB,IAAI,CAACL;IACnE,CAAC,CAAC;IACF,IAAIQ,UAAU,GAAG9B,SAAS,CAACM,IAAI,EAAEqB,IAAI,CAACJ,SAAS,EAAE,YAAY;MAC3D,OAAOK,QAAQ;IACjB,CAAC,EAAEd,KAAK,EAAEC,GAAG,CAAC;IACd,IAAIe,UAAU,EAAE;MACdf,GAAG,GAAGY,IAAI,CAACJ,SAAS,GAAGjB,IAAI,IAAIsB,QAAQ,GAAG,CAAC,GAAGlB,UAAU,CAAC;MACzDC,MAAM,CAACE,GAAG,GAAG,CAAC,CAAC,GAAGvC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEqD,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;QAC3DH,MAAM,EAAE;MACV,CAAC,CAAC;IACJ;EACF;EACA,IAAIO,KAAK,GAAGL,WAAW,GAAGb,GAAG,GAAG,CAAC,GAAGA,GAAG;EACvC,IAAImB,MAAM,GAAG,SAASA,MAAMA,CAAC5C,CAAC,EAAE;IAC9B,IAAI6B,KAAK,GAAGN,MAAM,CAACvB,CAAC,CAAC;IACrB,IAAI8B,IAAI;IACR,IAAIC,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAG;MAC/B,IAAID,IAAI,KAAKE,SAAS,EAAE;QACtBF,IAAI,GAAGV,WAAW,CAACS,KAAK,EAAE7B,CAAC,CAAC;MAC9B;MACA,OAAO8B,IAAI;IACb,CAAC;IACD,IAAI9B,CAAC,KAAK,CAAC,EAAE;MACX,IAAIiC,GAAG,GAAGf,IAAI,IAAIW,KAAK,CAACK,UAAU,GAAGhB,IAAI,GAAGa,OAAO,CAAC,CAAC,GAAG,CAAC,GAAGL,KAAK,CAAC;MAClEH,MAAM,CAACvB,CAAC,CAAC,GAAG6B,KAAK,GAAG3C,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE2C,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QAC9DM,SAAS,EAAEF,GAAG,GAAG,CAAC,GAAGJ,KAAK,CAACK,UAAU,GAAGD,GAAG,GAAGf,IAAI,GAAGW,KAAK,CAACK;MAC7D,CAAC,CAAC;IACJ,CAAC,MAAM;MACLX,MAAM,CAACvB,CAAC,CAAC,GAAG6B,KAAK,GAAG3C,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE2C,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QAC9DM,SAAS,EAAEN,KAAK,CAACK;MACnB,CAAC,CAAC;IACJ;IACA,IAAIE,MAAM,GAAGxB,SAAS,CAACM,IAAI,EAAEW,KAAK,CAACM,SAAS,EAAEJ,OAAO,EAAEL,KAAK,EAAEC,GAAG,CAAC;IAClE,IAAIS,MAAM,EAAE;MACVV,KAAK,GAAGG,KAAK,CAACM,SAAS,GAAGjB,IAAI,IAAIa,OAAO,CAAC,CAAC,GAAG,CAAC,GAAGT,UAAU,CAAC;MAC7DC,MAAM,CAACvB,CAAC,CAAC,GAAGd,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE2C,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QACtDO,MAAM,EAAE;MACV,CAAC,CAAC;IACJ;EACF,CAAC;EACD,KAAK,IAAIpC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2C,KAAK,EAAE3C,CAAC,EAAE,EAAE;IAC9B4C,MAAM,CAAC5C,CAAC,CAAC;EACX;EACA,OAAOuB,MAAM;AACf;AACA,OAAO,SAASsB,QAAQA,CAACC,KAAK,EAAEC,QAAQ,EAAEC,aAAa,EAAE;EACvD,IAAIC,IAAI,GAAGH,KAAK,CAACG,IAAI;IACnB5B,KAAK,GAAGyB,KAAK,CAACzB,KAAK;IACnB6B,OAAO,GAAGJ,KAAK,CAACI,OAAO;IACvB5B,UAAU,GAAGwB,KAAK,CAACxB,UAAU;IAC7B6B,WAAW,GAAGL,KAAK,CAACK,WAAW;IAC/BC,QAAQ,GAAGN,KAAK,CAACM,QAAQ;IACzBC,aAAa,GAAGP,KAAK,CAACO,aAAa;IACnCC,IAAI,GAAGR,KAAK,CAACQ,IAAI;IACjBC,KAAK,GAAGT,KAAK,CAACS,KAAK;EACrB,IAAI,CAAClC,KAAK,IAAI,CAACA,KAAK,CAACjC,MAAM,IAAI,CAAC6D,IAAI,EAAE;IACpC,OAAO,EAAE;EACX;EACA,IAAIxC,QAAQ,CAAC2C,QAAQ,CAAC,IAAIzC,MAAM,CAAC6C,KAAK,EAAE;IACtC,OAAO1C,sBAAsB,CAACO,KAAK,EAAE,OAAO+B,QAAQ,KAAK,QAAQ,IAAI3C,QAAQ,CAAC2C,QAAQ,CAAC,GAAGA,QAAQ,GAAG,CAAC,CAAC;EACzG;EACA,IAAIK,UAAU,GAAG,EAAE;EACnB,IAAIC,OAAO,GAAGP,WAAW,KAAK,KAAK,IAAIA,WAAW,KAAK,QAAQ,GAAG,OAAO,GAAG,QAAQ;EACpF,IAAIQ,QAAQ,GAAGL,IAAI,IAAII,OAAO,KAAK,OAAO,GAAGhD,aAAa,CAAC4C,IAAI,EAAE;IAC/DP,QAAQ,EAAEA,QAAQ;IAClBC,aAAa,EAAEA;EACjB,CAAC,CAAC,GAAG;IACHY,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE;EACV,CAAC;EACD,IAAIzC,WAAW,GAAG,SAASA,WAAWA,CAAC0C,OAAO,EAAEC,KAAK,EAAE;IACrD,IAAInE,KAAK,GAAGW,UAAU,CAAC8C,aAAa,CAAC,GAAGA,aAAa,CAACS,OAAO,CAAClE,KAAK,EAAEmE,KAAK,CAAC,GAAGD,OAAO,CAAClE,KAAK;IAC3F;IACA,OAAO8D,OAAO,KAAK,OAAO,GAAG3C,kBAAkB,CAACL,aAAa,CAACd,KAAK,EAAE;MACnEmD,QAAQ,EAAEA,QAAQ;MAClBC,aAAa,EAAEA;IACjB,CAAC,CAAC,EAAEW,QAAQ,EAAEJ,KAAK,CAAC,GAAG7C,aAAa,CAACd,KAAK,EAAE;MAC1CmD,QAAQ,EAAEA,QAAQ;MAClBC,aAAa,EAAEA;IACjB,CAAC,CAAC,CAACU,OAAO,CAAC;EACb,CAAC;EACD,IAAIxC,IAAI,GAAGG,KAAK,CAACjC,MAAM,IAAI,CAAC,GAAGoB,QAAQ,CAACa,KAAK,CAAC,CAAC,CAAC,CAACa,UAAU,GAAGb,KAAK,CAAC,CAAC,CAAC,CAACa,UAAU,CAAC,GAAG,CAAC;EACtF,IAAIf,UAAU,GAAGN,iBAAiB,CAACqC,OAAO,EAAEhC,IAAI,EAAEwC,OAAO,CAAC;EAC1D,IAAIN,QAAQ,KAAK,0BAA0B,EAAE;IAC3C,OAAOpC,mBAAmB,CAACE,IAAI,EAAEC,UAAU,EAAEC,WAAW,EAAEC,KAAK,EAAEC,UAAU,CAAC;EAC9E;EACA,IAAI8B,QAAQ,KAAK,eAAe,IAAIA,QAAQ,KAAK,kBAAkB,EAAE;IACnEK,UAAU,GAAGpB,aAAa,CAACnB,IAAI,EAAEC,UAAU,EAAEC,WAAW,EAAEC,KAAK,EAAEC,UAAU,EAAE8B,QAAQ,KAAK,kBAAkB,CAAC;EAC/G,CAAC,MAAM;IACLK,UAAU,GAAGxC,WAAW,CAACC,IAAI,EAAEC,UAAU,EAAEC,WAAW,EAAEC,KAAK,EAAEC,UAAU,CAAC;EAC5E;EACA,OAAOmC,UAAU,CAAC5E,MAAM,CAAC,UAAUgD,KAAK,EAAE;IACxC,OAAOA,KAAK,CAACO,MAAM;EACrB,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}