{"ast": null, "code": "/**\n * Removes `key` and its value from the hash.\n *\n * @private\n * @name delete\n * @memberOf Hash\n * @param {Object} hash The hash to modify.\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */\nfunction hashDelete(key) {\n  var result = this.has(key) && delete this.__data__[key];\n  this.size -= result ? 1 : 0;\n  return result;\n}\nmodule.exports = hashDelete;", "map": {"version": 3, "names": ["hashDelete", "key", "result", "has", "__data__", "size", "module", "exports"], "sources": ["/Users/<USER>/Downloads/interview-final/node_modules/lodash/_hashDelete.js"], "sourcesContent": ["/**\n * Removes `key` and its value from the hash.\n *\n * @private\n * @name delete\n * @memberOf Hash\n * @param {Object} hash The hash to modify.\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */\nfunction hashDelete(key) {\n  var result = this.has(key) && delete this.__data__[key];\n  this.size -= result ? 1 : 0;\n  return result;\n}\n\nmodule.exports = hashDelete;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,UAAUA,CAACC,GAAG,EAAE;EACvB,IAAIC,MAAM,GAAG,IAAI,CAACC,GAAG,CAACF,GAAG,CAAC,IAAI,OAAO,IAAI,CAACG,QAAQ,CAACH,GAAG,CAAC;EACvD,IAAI,CAACI,IAAI,IAAIH,MAAM,GAAG,CAAC,GAAG,CAAC;EAC3B,OAAOA,MAAM;AACf;AAEAI,MAAM,CAACC,OAAO,GAAGP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}