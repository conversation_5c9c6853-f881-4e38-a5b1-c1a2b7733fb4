{"ast": null, "code": "/**\n * Converts `map` to its key-value pairs.\n *\n * @private\n * @param {Object} map The map to convert.\n * @returns {Array} Returns the key-value pairs.\n */\nfunction mapToArray(map) {\n  var index = -1,\n    result = Array(map.size);\n  map.forEach(function (value, key) {\n    result[++index] = [key, value];\n  });\n  return result;\n}\nmodule.exports = mapToArray;", "map": {"version": 3, "names": ["mapToArray", "map", "index", "result", "Array", "size", "for<PERSON>ach", "value", "key", "module", "exports"], "sources": ["/Users/<USER>/Downloads/interview-final/frontend/node_modules/lodash/_mapToArray.js"], "sourcesContent": ["/**\n * Converts `map` to its key-value pairs.\n *\n * @private\n * @param {Object} map The map to convert.\n * @returns {Array} Returns the key-value pairs.\n */\nfunction mapToArray(map) {\n  var index = -1,\n      result = Array(map.size);\n\n  map.forEach(function(value, key) {\n    result[++index] = [key, value];\n  });\n  return result;\n}\n\nmodule.exports = mapToArray;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,UAAUA,CAACC,GAAG,EAAE;EACvB,IAAIC,KAAK,GAAG,CAAC,CAAC;IACVC,MAAM,GAAGC,KAAK,CAACH,GAAG,CAACI,IAAI,CAAC;EAE5BJ,GAAG,CAACK,OAAO,CAAC,UAASC,KAAK,EAAEC,GAAG,EAAE;IAC/BL,MAAM,CAAC,EAAED,KAAK,CAAC,GAAG,CAACM,GAAG,EAAED,KAAK,CAAC;EAChC,CAAC,CAAC;EACF,OAAOJ,MAAM;AACf;AAEAM,MAAM,CAACC,OAAO,GAAGV,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}