{"ast": null, "code": "var _excluded = [\"type\", \"layout\", \"connectNulls\", \"ref\"],\n  _excluded2 = [\"key\"];\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  for (var key in source) {\n    if (Object.prototype.hasOwnProperty.call(source, key)) {\n      if (excluded.indexOf(key) >= 0) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n  return arr2;\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nfunction _callSuper(t, o, e) {\n  return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e));\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) {\n    return call;\n  } else if (call !== void 0) {\n    throw new TypeError(\"Derived constructors may only return object or undefined\");\n  }\n  return _assertThisInitialized(self);\n}\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return self;\n}\nfunction _isNativeReflectConstruct() {\n  try {\n    var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n  } catch (t) {}\n  return (_isNativeReflectConstruct = function _isNativeReflectConstruct() {\n    return !!t;\n  })();\n}\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  Object.defineProperty(subClass, \"prototype\", {\n    writable: false\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n  return _setPrototypeOf(o, p);\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\n/**\n * @fileOverview Line\n */\nimport React, { PureComponent } from 'react';\nimport Animate from 'react-smooth';\nimport isFunction from 'lodash/isFunction';\nimport isNil from 'lodash/isNil';\nimport isEqual from 'lodash/isEqual';\nimport clsx from 'clsx';\nimport { Curve } from '../shape/Curve';\nimport { Dot } from '../shape/Dot';\nimport { Layer } from '../container/Layer';\nimport { LabelList } from '../component/LabelList';\nimport { ErrorBar } from './ErrorBar';\nimport { uniqueId, interpolateNumber } from '../util/DataUtils';\nimport { findAllByType, filterProps, hasClipDot } from '../util/ReactUtils';\nimport { Global } from '../util/Global';\nimport { getCateCoordinateOfLine, getValueByDataKey } from '../util/ChartUtils';\nexport var Line = /*#__PURE__*/function (_PureComponent) {\n  function Line() {\n    var _this;\n    _classCallCheck(this, Line);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _callSuper(this, Line, [].concat(args));\n    _defineProperty(_this, \"state\", {\n      isAnimationFinished: true,\n      totalLength: 0\n    });\n    _defineProperty(_this, \"generateSimpleStrokeDasharray\", function (totalLength, length) {\n      return \"\".concat(length, \"px \").concat(totalLength - length, \"px\");\n    });\n    _defineProperty(_this, \"getStrokeDasharray\", function (length, totalLength, lines) {\n      var lineLength = lines.reduce(function (pre, next) {\n        return pre + next;\n      });\n\n      // if lineLength is 0 return the default when no strokeDasharray is provided\n      if (!lineLength) {\n        return _this.generateSimpleStrokeDasharray(totalLength, length);\n      }\n      var count = Math.floor(length / lineLength);\n      var remainLength = length % lineLength;\n      var restLength = totalLength - length;\n      var remainLines = [];\n      for (var i = 0, sum = 0; i < lines.length; sum += lines[i], ++i) {\n        if (sum + lines[i] > remainLength) {\n          remainLines = [].concat(_toConsumableArray(lines.slice(0, i)), [remainLength - sum]);\n          break;\n        }\n      }\n      var emptyLines = remainLines.length % 2 === 0 ? [0, restLength] : [restLength];\n      return [].concat(_toConsumableArray(Line.repeat(lines, count)), _toConsumableArray(remainLines), emptyLines).map(function (line) {\n        return \"\".concat(line, \"px\");\n      }).join(', ');\n    });\n    _defineProperty(_this, \"id\", uniqueId('recharts-line-'));\n    _defineProperty(_this, \"pathRef\", function (node) {\n      _this.mainCurve = node;\n    });\n    _defineProperty(_this, \"handleAnimationEnd\", function () {\n      _this.setState({\n        isAnimationFinished: true\n      });\n      if (_this.props.onAnimationEnd) {\n        _this.props.onAnimationEnd();\n      }\n    });\n    _defineProperty(_this, \"handleAnimationStart\", function () {\n      _this.setState({\n        isAnimationFinished: false\n      });\n      if (_this.props.onAnimationStart) {\n        _this.props.onAnimationStart();\n      }\n    });\n    return _this;\n  }\n  _inherits(Line, _PureComponent);\n  return _createClass(Line, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      if (!this.props.isAnimationActive) {\n        return;\n      }\n      var totalLength = this.getTotalLength();\n      this.setState({\n        totalLength: totalLength\n      });\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate() {\n      if (!this.props.isAnimationActive) {\n        return;\n      }\n      var totalLength = this.getTotalLength();\n      if (totalLength !== this.state.totalLength) {\n        this.setState({\n          totalLength: totalLength\n        });\n      }\n    }\n  }, {\n    key: \"getTotalLength\",\n    value: function getTotalLength() {\n      var curveDom = this.mainCurve;\n      try {\n        return curveDom && curveDom.getTotalLength && curveDom.getTotalLength() || 0;\n      } catch (err) {\n        return 0;\n      }\n    }\n  }, {\n    key: \"renderErrorBar\",\n    value: function renderErrorBar(needClip, clipPathId) {\n      if (this.props.isAnimationActive && !this.state.isAnimationFinished) {\n        return null;\n      }\n      var _this$props = this.props,\n        points = _this$props.points,\n        xAxis = _this$props.xAxis,\n        yAxis = _this$props.yAxis,\n        layout = _this$props.layout,\n        children = _this$props.children;\n      var errorBarItems = findAllByType(children, ErrorBar);\n      if (!errorBarItems) {\n        return null;\n      }\n      var dataPointFormatter = function dataPointFormatter(dataPoint, dataKey) {\n        return {\n          x: dataPoint.x,\n          y: dataPoint.y,\n          value: dataPoint.value,\n          errorVal: getValueByDataKey(dataPoint.payload, dataKey)\n        };\n      };\n      var errorBarProps = {\n        clipPath: needClip ? \"url(#clipPath-\".concat(clipPathId, \")\") : null\n      };\n      return /*#__PURE__*/React.createElement(Layer, errorBarProps, errorBarItems.map(function (item) {\n        return /*#__PURE__*/React.cloneElement(item, {\n          key: \"bar-\".concat(item.props.dataKey),\n          data: points,\n          xAxis: xAxis,\n          yAxis: yAxis,\n          layout: layout,\n          dataPointFormatter: dataPointFormatter\n        });\n      }));\n    }\n  }, {\n    key: \"renderDots\",\n    value: function renderDots(needClip, clipDot, clipPathId) {\n      var isAnimationActive = this.props.isAnimationActive;\n      if (isAnimationActive && !this.state.isAnimationFinished) {\n        return null;\n      }\n      var _this$props2 = this.props,\n        dot = _this$props2.dot,\n        points = _this$props2.points,\n        dataKey = _this$props2.dataKey;\n      var lineProps = filterProps(this.props, false);\n      var customDotProps = filterProps(dot, true);\n      var dots = points.map(function (entry, i) {\n        var dotProps = _objectSpread(_objectSpread(_objectSpread({\n          key: \"dot-\".concat(i),\n          r: 3\n        }, lineProps), customDotProps), {}, {\n          index: i,\n          cx: entry.x,\n          cy: entry.y,\n          value: entry.value,\n          dataKey: dataKey,\n          payload: entry.payload,\n          points: points\n        });\n        return Line.renderDotItem(dot, dotProps);\n      });\n      var dotsProps = {\n        clipPath: needClip ? \"url(#clipPath-\".concat(clipDot ? '' : 'dots-').concat(clipPathId, \")\") : null\n      };\n      return /*#__PURE__*/React.createElement(Layer, _extends({\n        className: \"recharts-line-dots\",\n        key: \"dots\"\n      }, dotsProps), dots);\n    }\n  }, {\n    key: \"renderCurveStatically\",\n    value: function renderCurveStatically(points, needClip, clipPathId, props) {\n      var _this$props3 = this.props,\n        type = _this$props3.type,\n        layout = _this$props3.layout,\n        connectNulls = _this$props3.connectNulls,\n        ref = _this$props3.ref,\n        others = _objectWithoutProperties(_this$props3, _excluded);\n      var curveProps = _objectSpread(_objectSpread(_objectSpread({}, filterProps(others, true)), {}, {\n        fill: 'none',\n        className: 'recharts-line-curve',\n        clipPath: needClip ? \"url(#clipPath-\".concat(clipPathId, \")\") : null,\n        points: points\n      }, props), {}, {\n        type: type,\n        layout: layout,\n        connectNulls: connectNulls\n      });\n      return /*#__PURE__*/React.createElement(Curve, _extends({}, curveProps, {\n        pathRef: this.pathRef\n      }));\n    }\n  }, {\n    key: \"renderCurveWithAnimation\",\n    value: function renderCurveWithAnimation(needClip, clipPathId) {\n      var _this2 = this;\n      var _this$props4 = this.props,\n        points = _this$props4.points,\n        strokeDasharray = _this$props4.strokeDasharray,\n        isAnimationActive = _this$props4.isAnimationActive,\n        animationBegin = _this$props4.animationBegin,\n        animationDuration = _this$props4.animationDuration,\n        animationEasing = _this$props4.animationEasing,\n        animationId = _this$props4.animationId,\n        animateNewValues = _this$props4.animateNewValues,\n        width = _this$props4.width,\n        height = _this$props4.height;\n      var _this$state = this.state,\n        prevPoints = _this$state.prevPoints,\n        totalLength = _this$state.totalLength;\n      return /*#__PURE__*/React.createElement(Animate, {\n        begin: animationBegin,\n        duration: animationDuration,\n        isActive: isAnimationActive,\n        easing: animationEasing,\n        from: {\n          t: 0\n        },\n        to: {\n          t: 1\n        },\n        key: \"line-\".concat(animationId),\n        onAnimationEnd: this.handleAnimationEnd,\n        onAnimationStart: this.handleAnimationStart\n      }, function (_ref) {\n        var t = _ref.t;\n        if (prevPoints) {\n          var prevPointsDiffFactor = prevPoints.length / points.length;\n          var stepData = points.map(function (entry, index) {\n            var prevPointIndex = Math.floor(index * prevPointsDiffFactor);\n            if (prevPoints[prevPointIndex]) {\n              var prev = prevPoints[prevPointIndex];\n              var interpolatorX = interpolateNumber(prev.x, entry.x);\n              var interpolatorY = interpolateNumber(prev.y, entry.y);\n              return _objectSpread(_objectSpread({}, entry), {}, {\n                x: interpolatorX(t),\n                y: interpolatorY(t)\n              });\n            }\n\n            // magic number of faking previous x and y location\n            if (animateNewValues) {\n              var _interpolatorX = interpolateNumber(width * 2, entry.x);\n              var _interpolatorY = interpolateNumber(height / 2, entry.y);\n              return _objectSpread(_objectSpread({}, entry), {}, {\n                x: _interpolatorX(t),\n                y: _interpolatorY(t)\n              });\n            }\n            return _objectSpread(_objectSpread({}, entry), {}, {\n              x: entry.x,\n              y: entry.y\n            });\n          });\n          return _this2.renderCurveStatically(stepData, needClip, clipPathId);\n        }\n        var interpolator = interpolateNumber(0, totalLength);\n        var curLength = interpolator(t);\n        var currentStrokeDasharray;\n        if (strokeDasharray) {\n          var lines = \"\".concat(strokeDasharray).split(/[,\\s]+/gim).map(function (num) {\n            return parseFloat(num);\n          });\n          currentStrokeDasharray = _this2.getStrokeDasharray(curLength, totalLength, lines);\n        } else {\n          currentStrokeDasharray = _this2.generateSimpleStrokeDasharray(totalLength, curLength);\n        }\n        return _this2.renderCurveStatically(points, needClip, clipPathId, {\n          strokeDasharray: currentStrokeDasharray\n        });\n      });\n    }\n  }, {\n    key: \"renderCurve\",\n    value: function renderCurve(needClip, clipPathId) {\n      var _this$props5 = this.props,\n        points = _this$props5.points,\n        isAnimationActive = _this$props5.isAnimationActive;\n      var _this$state2 = this.state,\n        prevPoints = _this$state2.prevPoints,\n        totalLength = _this$state2.totalLength;\n      if (isAnimationActive && points && points.length && (!prevPoints && totalLength > 0 || !isEqual(prevPoints, points))) {\n        return this.renderCurveWithAnimation(needClip, clipPathId);\n      }\n      return this.renderCurveStatically(points, needClip, clipPathId);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _filterProps;\n      var _this$props6 = this.props,\n        hide = _this$props6.hide,\n        dot = _this$props6.dot,\n        points = _this$props6.points,\n        className = _this$props6.className,\n        xAxis = _this$props6.xAxis,\n        yAxis = _this$props6.yAxis,\n        top = _this$props6.top,\n        left = _this$props6.left,\n        width = _this$props6.width,\n        height = _this$props6.height,\n        isAnimationActive = _this$props6.isAnimationActive,\n        id = _this$props6.id;\n      if (hide || !points || !points.length) {\n        return null;\n      }\n      var isAnimationFinished = this.state.isAnimationFinished;\n      var hasSinglePoint = points.length === 1;\n      var layerClass = clsx('recharts-line', className);\n      var needClipX = xAxis && xAxis.allowDataOverflow;\n      var needClipY = yAxis && yAxis.allowDataOverflow;\n      var needClip = needClipX || needClipY;\n      var clipPathId = isNil(id) ? this.id : id;\n      var _ref2 = (_filterProps = filterProps(dot, false)) !== null && _filterProps !== void 0 ? _filterProps : {\n          r: 3,\n          strokeWidth: 2\n        },\n        _ref2$r = _ref2.r,\n        r = _ref2$r === void 0 ? 3 : _ref2$r,\n        _ref2$strokeWidth = _ref2.strokeWidth,\n        strokeWidth = _ref2$strokeWidth === void 0 ? 2 : _ref2$strokeWidth;\n      var _ref3 = hasClipDot(dot) ? dot : {},\n        _ref3$clipDot = _ref3.clipDot,\n        clipDot = _ref3$clipDot === void 0 ? true : _ref3$clipDot;\n      var dotSize = r * 2 + strokeWidth;\n      return /*#__PURE__*/React.createElement(Layer, {\n        className: layerClass\n      }, needClipX || needClipY ? /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"clipPath\", {\n        id: \"clipPath-\".concat(clipPathId)\n      }, /*#__PURE__*/React.createElement(\"rect\", {\n        x: needClipX ? left : left - width / 2,\n        y: needClipY ? top : top - height / 2,\n        width: needClipX ? width : width * 2,\n        height: needClipY ? height : height * 2\n      })), !clipDot && /*#__PURE__*/React.createElement(\"clipPath\", {\n        id: \"clipPath-dots-\".concat(clipPathId)\n      }, /*#__PURE__*/React.createElement(\"rect\", {\n        x: left - dotSize / 2,\n        y: top - dotSize / 2,\n        width: width + dotSize,\n        height: height + dotSize\n      }))) : null, !hasSinglePoint && this.renderCurve(needClip, clipPathId), this.renderErrorBar(needClip, clipPathId), (hasSinglePoint || dot) && this.renderDots(needClip, clipDot, clipPathId), (!isAnimationActive || isAnimationFinished) && LabelList.renderCallByParent(this.props, points));\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(nextProps, prevState) {\n      if (nextProps.animationId !== prevState.prevAnimationId) {\n        return {\n          prevAnimationId: nextProps.animationId,\n          curPoints: nextProps.points,\n          prevPoints: prevState.curPoints\n        };\n      }\n      if (nextProps.points !== prevState.curPoints) {\n        return {\n          curPoints: nextProps.points\n        };\n      }\n      return null;\n    }\n  }, {\n    key: \"repeat\",\n    value: function repeat(lines, count) {\n      var linesUnit = lines.length % 2 !== 0 ? [].concat(_toConsumableArray(lines), [0]) : lines;\n      var result = [];\n      for (var i = 0; i < count; ++i) {\n        result = [].concat(_toConsumableArray(result), _toConsumableArray(linesUnit));\n      }\n      return result;\n    }\n  }, {\n    key: \"renderDotItem\",\n    value: function renderDotItem(option, props) {\n      var dotItem;\n      if (/*#__PURE__*/React.isValidElement(option)) {\n        dotItem = /*#__PURE__*/React.cloneElement(option, props);\n      } else if (isFunction(option)) {\n        dotItem = option(props);\n      } else {\n        var key = props.key,\n          dotProps = _objectWithoutProperties(props, _excluded2);\n        var className = clsx('recharts-line-dot', typeof option !== 'boolean' ? option.className : '');\n        dotItem = /*#__PURE__*/React.createElement(Dot, _extends({\n          key: key\n        }, dotProps, {\n          className: className\n        }));\n      }\n      return dotItem;\n    }\n  }]);\n}(PureComponent);\n_defineProperty(Line, \"displayName\", 'Line');\n_defineProperty(Line, \"defaultProps\", {\n  xAxisId: 0,\n  yAxisId: 0,\n  connectNulls: false,\n  activeDot: true,\n  dot: true,\n  legendType: 'line',\n  stroke: '#3182bd',\n  strokeWidth: 1,\n  fill: '#fff',\n  points: [],\n  isAnimationActive: !Global.isSsr,\n  animateNewValues: true,\n  animationBegin: 0,\n  animationDuration: 1500,\n  animationEasing: 'ease',\n  hide: false,\n  label: false\n});\n/**\n * Compose the data of each group\n * @param {Object} props The props from the component\n * @param  {Object} xAxis   The configuration of x-axis\n * @param  {Object} yAxis   The configuration of y-axis\n * @param  {String} dataKey The unique key of a group\n * @return {Array}  Composed data\n */\n_defineProperty(Line, \"getComposedData\", function (_ref4) {\n  var props = _ref4.props,\n    xAxis = _ref4.xAxis,\n    yAxis = _ref4.yAxis,\n    xAxisTicks = _ref4.xAxisTicks,\n    yAxisTicks = _ref4.yAxisTicks,\n    dataKey = _ref4.dataKey,\n    bandSize = _ref4.bandSize,\n    displayedData = _ref4.displayedData,\n    offset = _ref4.offset;\n  var layout = props.layout;\n  var points = displayedData.map(function (entry, index) {\n    var value = getValueByDataKey(entry, dataKey);\n    if (layout === 'horizontal') {\n      return {\n        x: getCateCoordinateOfLine({\n          axis: xAxis,\n          ticks: xAxisTicks,\n          bandSize: bandSize,\n          entry: entry,\n          index: index\n        }),\n        y: isNil(value) ? null : yAxis.scale(value),\n        value: value,\n        payload: entry\n      };\n    }\n    return {\n      x: isNil(value) ? null : xAxis.scale(value),\n      y: getCateCoordinateOfLine({\n        axis: yAxis,\n        ticks: yAxisTicks,\n        bandSize: bandSize,\n        entry: entry,\n        index: index\n      }),\n      value: value,\n      payload: entry\n    };\n  });\n  return _objectSpread({\n    points: points,\n    layout: layout\n  }, offset);\n});", "map": {"version": 3, "names": ["_excluded", "_excluded2", "_typeof", "o", "Symbol", "iterator", "constructor", "prototype", "_objectWithoutProperties", "source", "excluded", "target", "_objectWithoutPropertiesLoose", "key", "i", "Object", "getOwnPropertySymbols", "sourceSymbolKeys", "length", "indexOf", "propertyIsEnumerable", "call", "hasOwnProperty", "_extends", "assign", "bind", "arguments", "apply", "ownKeys", "e", "r", "t", "keys", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "_objectSpread", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_toConsumableArray", "arr", "_arrayWithoutHoles", "_iterableToArray", "_unsupportedIterableToArray", "_nonIterableSpread", "TypeError", "minLen", "_arrayLikeToArray", "n", "toString", "slice", "name", "Array", "from", "test", "iter", "isArray", "len", "arr2", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "_defineProperties", "props", "descriptor", "configurable", "writable", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_createClass", "protoProps", "staticProps", "_callSuper", "_getPrototypeOf", "_possibleConstructorReturn", "_isNativeReflectConstruct", "Reflect", "construct", "self", "_assertThisInitialized", "ReferenceError", "Boolean", "valueOf", "setPrototypeOf", "getPrototypeOf", "__proto__", "_inherits", "subClass", "superClass", "create", "value", "_setPrototypeOf", "p", "obj", "_toPrimitive", "toPrimitive", "String", "Number", "React", "PureComponent", "Animate", "isFunction", "isNil", "isEqual", "clsx", "Curve", "Dot", "Layer", "LabelList", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "uniqueId", "interpolateNumber", "findAllByType", "filterProps", "hasClipDot", "Global", "getCateCoordinateOfLine", "getValueByDataKey", "Line", "_PureComponent", "_this", "_len", "args", "_key", "concat", "isAnimationFinished", "totalLength", "lines", "lineLength", "reduce", "pre", "next", "generateSimpleStrokeDasharray", "count", "Math", "floor", "<PERSON><PERSON><PERSON><PERSON>", "restLength", "remainLines", "sum", "emptyLines", "repeat", "map", "line", "join", "node", "mainCurve", "setState", "onAnimationEnd", "onAnimationStart", "componentDidMount", "isAnimationActive", "getTotalLength", "componentDidUpdate", "state", "curveDom", "err", "renderErrorBar", "needClip", "clipPathId", "_this$props", "points", "xAxis", "yAxis", "layout", "children", "errorBarItems", "dataPointFormatter", "dataPoint", "dataKey", "x", "y", "errorVal", "payload", "errorBarProps", "clipPath", "createElement", "item", "cloneElement", "data", "renderDots", "clipDot", "_this$props2", "dot", "lineProps", "customDotProps", "dots", "entry", "dotProps", "index", "cx", "cy", "renderDotItem", "dotsProps", "className", "renderCurveStatically", "_this$props3", "type", "connectNulls", "ref", "others", "curveProps", "fill", "pathRef", "renderCurveWithAnimation", "_this2", "_this$props4", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animationBegin", "animationDuration", "animationEasing", "animationId", "animate<PERSON>ew<PERSON><PERSON><PERSON>", "width", "height", "_this$state", "prevPoints", "begin", "duration", "isActive", "easing", "to", "handleAnimationEnd", "handleAnimationStart", "_ref", "prevPointsDiffFactor", "stepData", "prevPointIndex", "prev", "interpolatorX", "interpolatorY", "_interpolatorX", "_interpolatorY", "interpolator", "curL<PERSON>th", "currentStrokeDasharray", "split", "num", "parseFloat", "getStrokeDasharray", "renderCurve", "_this$props5", "_this$state2", "render", "_filterProps", "_this$props6", "hide", "top", "left", "id", "hasSinglePoint", "layerClass", "needClipX", "allowDataOverflow", "needClipY", "_ref2", "strokeWidth", "_ref2$r", "_ref2$strokeWidth", "_ref3", "_ref3$clipDot", "dotSize", "renderCallByParent", "getDerivedStateFromProps", "nextProps", "prevState", "prevAnimationId", "curPoints", "linesUnit", "result", "option", "dotItem", "isValidElement", "xAxisId", "yAxisId", "activeDot", "legendType", "stroke", "isSsr", "label", "_ref4", "xAxisTicks", "yAxisTicks", "bandSize", "displayedData", "offset", "axis", "ticks", "scale"], "sources": ["/Users/<USER>/Downloads/interview-final/frontend/node_modules/recharts/es6/cartesian/Line.js"], "sourcesContent": ["var _excluded = [\"type\", \"layout\", \"connectNulls\", \"ref\"],\n  _excluded2 = [\"key\"];\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } } return target; }\nfunction _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter); }\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) { return call; } else if (call !== void 0) { throw new TypeError(\"Derived constructors may only return object or undefined\"); } return _assertThisInitialized(self); }\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return self; }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function\"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, \"prototype\", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\n/**\n * @fileOverview Line\n */\nimport React, { PureComponent } from 'react';\nimport Animate from 'react-smooth';\nimport isFunction from 'lodash/isFunction';\nimport isNil from 'lodash/isNil';\nimport isEqual from 'lodash/isEqual';\nimport clsx from 'clsx';\nimport { Curve } from '../shape/Curve';\nimport { Dot } from '../shape/Dot';\nimport { Layer } from '../container/Layer';\nimport { LabelList } from '../component/LabelList';\nimport { ErrorBar } from './ErrorBar';\nimport { uniqueId, interpolateNumber } from '../util/DataUtils';\nimport { findAllByType, filterProps, hasClipDot } from '../util/ReactUtils';\nimport { Global } from '../util/Global';\nimport { getCateCoordinateOfLine, getValueByDataKey } from '../util/ChartUtils';\nexport var Line = /*#__PURE__*/function (_PureComponent) {\n  function Line() {\n    var _this;\n    _classCallCheck(this, Line);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _callSuper(this, Line, [].concat(args));\n    _defineProperty(_this, \"state\", {\n      isAnimationFinished: true,\n      totalLength: 0\n    });\n    _defineProperty(_this, \"generateSimpleStrokeDasharray\", function (totalLength, length) {\n      return \"\".concat(length, \"px \").concat(totalLength - length, \"px\");\n    });\n    _defineProperty(_this, \"getStrokeDasharray\", function (length, totalLength, lines) {\n      var lineLength = lines.reduce(function (pre, next) {\n        return pre + next;\n      });\n\n      // if lineLength is 0 return the default when no strokeDasharray is provided\n      if (!lineLength) {\n        return _this.generateSimpleStrokeDasharray(totalLength, length);\n      }\n      var count = Math.floor(length / lineLength);\n      var remainLength = length % lineLength;\n      var restLength = totalLength - length;\n      var remainLines = [];\n      for (var i = 0, sum = 0; i < lines.length; sum += lines[i], ++i) {\n        if (sum + lines[i] > remainLength) {\n          remainLines = [].concat(_toConsumableArray(lines.slice(0, i)), [remainLength - sum]);\n          break;\n        }\n      }\n      var emptyLines = remainLines.length % 2 === 0 ? [0, restLength] : [restLength];\n      return [].concat(_toConsumableArray(Line.repeat(lines, count)), _toConsumableArray(remainLines), emptyLines).map(function (line) {\n        return \"\".concat(line, \"px\");\n      }).join(', ');\n    });\n    _defineProperty(_this, \"id\", uniqueId('recharts-line-'));\n    _defineProperty(_this, \"pathRef\", function (node) {\n      _this.mainCurve = node;\n    });\n    _defineProperty(_this, \"handleAnimationEnd\", function () {\n      _this.setState({\n        isAnimationFinished: true\n      });\n      if (_this.props.onAnimationEnd) {\n        _this.props.onAnimationEnd();\n      }\n    });\n    _defineProperty(_this, \"handleAnimationStart\", function () {\n      _this.setState({\n        isAnimationFinished: false\n      });\n      if (_this.props.onAnimationStart) {\n        _this.props.onAnimationStart();\n      }\n    });\n    return _this;\n  }\n  _inherits(Line, _PureComponent);\n  return _createClass(Line, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      if (!this.props.isAnimationActive) {\n        return;\n      }\n      var totalLength = this.getTotalLength();\n      this.setState({\n        totalLength: totalLength\n      });\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate() {\n      if (!this.props.isAnimationActive) {\n        return;\n      }\n      var totalLength = this.getTotalLength();\n      if (totalLength !== this.state.totalLength) {\n        this.setState({\n          totalLength: totalLength\n        });\n      }\n    }\n  }, {\n    key: \"getTotalLength\",\n    value: function getTotalLength() {\n      var curveDom = this.mainCurve;\n      try {\n        return curveDom && curveDom.getTotalLength && curveDom.getTotalLength() || 0;\n      } catch (err) {\n        return 0;\n      }\n    }\n  }, {\n    key: \"renderErrorBar\",\n    value: function renderErrorBar(needClip, clipPathId) {\n      if (this.props.isAnimationActive && !this.state.isAnimationFinished) {\n        return null;\n      }\n      var _this$props = this.props,\n        points = _this$props.points,\n        xAxis = _this$props.xAxis,\n        yAxis = _this$props.yAxis,\n        layout = _this$props.layout,\n        children = _this$props.children;\n      var errorBarItems = findAllByType(children, ErrorBar);\n      if (!errorBarItems) {\n        return null;\n      }\n      var dataPointFormatter = function dataPointFormatter(dataPoint, dataKey) {\n        return {\n          x: dataPoint.x,\n          y: dataPoint.y,\n          value: dataPoint.value,\n          errorVal: getValueByDataKey(dataPoint.payload, dataKey)\n        };\n      };\n      var errorBarProps = {\n        clipPath: needClip ? \"url(#clipPath-\".concat(clipPathId, \")\") : null\n      };\n      return /*#__PURE__*/React.createElement(Layer, errorBarProps, errorBarItems.map(function (item) {\n        return /*#__PURE__*/React.cloneElement(item, {\n          key: \"bar-\".concat(item.props.dataKey),\n          data: points,\n          xAxis: xAxis,\n          yAxis: yAxis,\n          layout: layout,\n          dataPointFormatter: dataPointFormatter\n        });\n      }));\n    }\n  }, {\n    key: \"renderDots\",\n    value: function renderDots(needClip, clipDot, clipPathId) {\n      var isAnimationActive = this.props.isAnimationActive;\n      if (isAnimationActive && !this.state.isAnimationFinished) {\n        return null;\n      }\n      var _this$props2 = this.props,\n        dot = _this$props2.dot,\n        points = _this$props2.points,\n        dataKey = _this$props2.dataKey;\n      var lineProps = filterProps(this.props, false);\n      var customDotProps = filterProps(dot, true);\n      var dots = points.map(function (entry, i) {\n        var dotProps = _objectSpread(_objectSpread(_objectSpread({\n          key: \"dot-\".concat(i),\n          r: 3\n        }, lineProps), customDotProps), {}, {\n          index: i,\n          cx: entry.x,\n          cy: entry.y,\n          value: entry.value,\n          dataKey: dataKey,\n          payload: entry.payload,\n          points: points\n        });\n        return Line.renderDotItem(dot, dotProps);\n      });\n      var dotsProps = {\n        clipPath: needClip ? \"url(#clipPath-\".concat(clipDot ? '' : 'dots-').concat(clipPathId, \")\") : null\n      };\n      return /*#__PURE__*/React.createElement(Layer, _extends({\n        className: \"recharts-line-dots\",\n        key: \"dots\"\n      }, dotsProps), dots);\n    }\n  }, {\n    key: \"renderCurveStatically\",\n    value: function renderCurveStatically(points, needClip, clipPathId, props) {\n      var _this$props3 = this.props,\n        type = _this$props3.type,\n        layout = _this$props3.layout,\n        connectNulls = _this$props3.connectNulls,\n        ref = _this$props3.ref,\n        others = _objectWithoutProperties(_this$props3, _excluded);\n      var curveProps = _objectSpread(_objectSpread(_objectSpread({}, filterProps(others, true)), {}, {\n        fill: 'none',\n        className: 'recharts-line-curve',\n        clipPath: needClip ? \"url(#clipPath-\".concat(clipPathId, \")\") : null,\n        points: points\n      }, props), {}, {\n        type: type,\n        layout: layout,\n        connectNulls: connectNulls\n      });\n      return /*#__PURE__*/React.createElement(Curve, _extends({}, curveProps, {\n        pathRef: this.pathRef\n      }));\n    }\n  }, {\n    key: \"renderCurveWithAnimation\",\n    value: function renderCurveWithAnimation(needClip, clipPathId) {\n      var _this2 = this;\n      var _this$props4 = this.props,\n        points = _this$props4.points,\n        strokeDasharray = _this$props4.strokeDasharray,\n        isAnimationActive = _this$props4.isAnimationActive,\n        animationBegin = _this$props4.animationBegin,\n        animationDuration = _this$props4.animationDuration,\n        animationEasing = _this$props4.animationEasing,\n        animationId = _this$props4.animationId,\n        animateNewValues = _this$props4.animateNewValues,\n        width = _this$props4.width,\n        height = _this$props4.height;\n      var _this$state = this.state,\n        prevPoints = _this$state.prevPoints,\n        totalLength = _this$state.totalLength;\n      return /*#__PURE__*/React.createElement(Animate, {\n        begin: animationBegin,\n        duration: animationDuration,\n        isActive: isAnimationActive,\n        easing: animationEasing,\n        from: {\n          t: 0\n        },\n        to: {\n          t: 1\n        },\n        key: \"line-\".concat(animationId),\n        onAnimationEnd: this.handleAnimationEnd,\n        onAnimationStart: this.handleAnimationStart\n      }, function (_ref) {\n        var t = _ref.t;\n        if (prevPoints) {\n          var prevPointsDiffFactor = prevPoints.length / points.length;\n          var stepData = points.map(function (entry, index) {\n            var prevPointIndex = Math.floor(index * prevPointsDiffFactor);\n            if (prevPoints[prevPointIndex]) {\n              var prev = prevPoints[prevPointIndex];\n              var interpolatorX = interpolateNumber(prev.x, entry.x);\n              var interpolatorY = interpolateNumber(prev.y, entry.y);\n              return _objectSpread(_objectSpread({}, entry), {}, {\n                x: interpolatorX(t),\n                y: interpolatorY(t)\n              });\n            }\n\n            // magic number of faking previous x and y location\n            if (animateNewValues) {\n              var _interpolatorX = interpolateNumber(width * 2, entry.x);\n              var _interpolatorY = interpolateNumber(height / 2, entry.y);\n              return _objectSpread(_objectSpread({}, entry), {}, {\n                x: _interpolatorX(t),\n                y: _interpolatorY(t)\n              });\n            }\n            return _objectSpread(_objectSpread({}, entry), {}, {\n              x: entry.x,\n              y: entry.y\n            });\n          });\n          return _this2.renderCurveStatically(stepData, needClip, clipPathId);\n        }\n        var interpolator = interpolateNumber(0, totalLength);\n        var curLength = interpolator(t);\n        var currentStrokeDasharray;\n        if (strokeDasharray) {\n          var lines = \"\".concat(strokeDasharray).split(/[,\\s]+/gim).map(function (num) {\n            return parseFloat(num);\n          });\n          currentStrokeDasharray = _this2.getStrokeDasharray(curLength, totalLength, lines);\n        } else {\n          currentStrokeDasharray = _this2.generateSimpleStrokeDasharray(totalLength, curLength);\n        }\n        return _this2.renderCurveStatically(points, needClip, clipPathId, {\n          strokeDasharray: currentStrokeDasharray\n        });\n      });\n    }\n  }, {\n    key: \"renderCurve\",\n    value: function renderCurve(needClip, clipPathId) {\n      var _this$props5 = this.props,\n        points = _this$props5.points,\n        isAnimationActive = _this$props5.isAnimationActive;\n      var _this$state2 = this.state,\n        prevPoints = _this$state2.prevPoints,\n        totalLength = _this$state2.totalLength;\n      if (isAnimationActive && points && points.length && (!prevPoints && totalLength > 0 || !isEqual(prevPoints, points))) {\n        return this.renderCurveWithAnimation(needClip, clipPathId);\n      }\n      return this.renderCurveStatically(points, needClip, clipPathId);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _filterProps;\n      var _this$props6 = this.props,\n        hide = _this$props6.hide,\n        dot = _this$props6.dot,\n        points = _this$props6.points,\n        className = _this$props6.className,\n        xAxis = _this$props6.xAxis,\n        yAxis = _this$props6.yAxis,\n        top = _this$props6.top,\n        left = _this$props6.left,\n        width = _this$props6.width,\n        height = _this$props6.height,\n        isAnimationActive = _this$props6.isAnimationActive,\n        id = _this$props6.id;\n      if (hide || !points || !points.length) {\n        return null;\n      }\n      var isAnimationFinished = this.state.isAnimationFinished;\n      var hasSinglePoint = points.length === 1;\n      var layerClass = clsx('recharts-line', className);\n      var needClipX = xAxis && xAxis.allowDataOverflow;\n      var needClipY = yAxis && yAxis.allowDataOverflow;\n      var needClip = needClipX || needClipY;\n      var clipPathId = isNil(id) ? this.id : id;\n      var _ref2 = (_filterProps = filterProps(dot, false)) !== null && _filterProps !== void 0 ? _filterProps : {\n          r: 3,\n          strokeWidth: 2\n        },\n        _ref2$r = _ref2.r,\n        r = _ref2$r === void 0 ? 3 : _ref2$r,\n        _ref2$strokeWidth = _ref2.strokeWidth,\n        strokeWidth = _ref2$strokeWidth === void 0 ? 2 : _ref2$strokeWidth;\n      var _ref3 = hasClipDot(dot) ? dot : {},\n        _ref3$clipDot = _ref3.clipDot,\n        clipDot = _ref3$clipDot === void 0 ? true : _ref3$clipDot;\n      var dotSize = r * 2 + strokeWidth;\n      return /*#__PURE__*/React.createElement(Layer, {\n        className: layerClass\n      }, needClipX || needClipY ? /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"clipPath\", {\n        id: \"clipPath-\".concat(clipPathId)\n      }, /*#__PURE__*/React.createElement(\"rect\", {\n        x: needClipX ? left : left - width / 2,\n        y: needClipY ? top : top - height / 2,\n        width: needClipX ? width : width * 2,\n        height: needClipY ? height : height * 2\n      })), !clipDot && /*#__PURE__*/React.createElement(\"clipPath\", {\n        id: \"clipPath-dots-\".concat(clipPathId)\n      }, /*#__PURE__*/React.createElement(\"rect\", {\n        x: left - dotSize / 2,\n        y: top - dotSize / 2,\n        width: width + dotSize,\n        height: height + dotSize\n      }))) : null, !hasSinglePoint && this.renderCurve(needClip, clipPathId), this.renderErrorBar(needClip, clipPathId), (hasSinglePoint || dot) && this.renderDots(needClip, clipDot, clipPathId), (!isAnimationActive || isAnimationFinished) && LabelList.renderCallByParent(this.props, points));\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(nextProps, prevState) {\n      if (nextProps.animationId !== prevState.prevAnimationId) {\n        return {\n          prevAnimationId: nextProps.animationId,\n          curPoints: nextProps.points,\n          prevPoints: prevState.curPoints\n        };\n      }\n      if (nextProps.points !== prevState.curPoints) {\n        return {\n          curPoints: nextProps.points\n        };\n      }\n      return null;\n    }\n  }, {\n    key: \"repeat\",\n    value: function repeat(lines, count) {\n      var linesUnit = lines.length % 2 !== 0 ? [].concat(_toConsumableArray(lines), [0]) : lines;\n      var result = [];\n      for (var i = 0; i < count; ++i) {\n        result = [].concat(_toConsumableArray(result), _toConsumableArray(linesUnit));\n      }\n      return result;\n    }\n  }, {\n    key: \"renderDotItem\",\n    value: function renderDotItem(option, props) {\n      var dotItem;\n      if ( /*#__PURE__*/React.isValidElement(option)) {\n        dotItem = /*#__PURE__*/React.cloneElement(option, props);\n      } else if (isFunction(option)) {\n        dotItem = option(props);\n      } else {\n        var key = props.key,\n          dotProps = _objectWithoutProperties(props, _excluded2);\n        var className = clsx('recharts-line-dot', typeof option !== 'boolean' ? option.className : '');\n        dotItem = /*#__PURE__*/React.createElement(Dot, _extends({\n          key: key\n        }, dotProps, {\n          className: className\n        }));\n      }\n      return dotItem;\n    }\n  }]);\n}(PureComponent);\n_defineProperty(Line, \"displayName\", 'Line');\n_defineProperty(Line, \"defaultProps\", {\n  xAxisId: 0,\n  yAxisId: 0,\n  connectNulls: false,\n  activeDot: true,\n  dot: true,\n  legendType: 'line',\n  stroke: '#3182bd',\n  strokeWidth: 1,\n  fill: '#fff',\n  points: [],\n  isAnimationActive: !Global.isSsr,\n  animateNewValues: true,\n  animationBegin: 0,\n  animationDuration: 1500,\n  animationEasing: 'ease',\n  hide: false,\n  label: false\n});\n/**\n * Compose the data of each group\n * @param {Object} props The props from the component\n * @param  {Object} xAxis   The configuration of x-axis\n * @param  {Object} yAxis   The configuration of y-axis\n * @param  {String} dataKey The unique key of a group\n * @return {Array}  Composed data\n */\n_defineProperty(Line, \"getComposedData\", function (_ref4) {\n  var props = _ref4.props,\n    xAxis = _ref4.xAxis,\n    yAxis = _ref4.yAxis,\n    xAxisTicks = _ref4.xAxisTicks,\n    yAxisTicks = _ref4.yAxisTicks,\n    dataKey = _ref4.dataKey,\n    bandSize = _ref4.bandSize,\n    displayedData = _ref4.displayedData,\n    offset = _ref4.offset;\n  var layout = props.layout;\n  var points = displayedData.map(function (entry, index) {\n    var value = getValueByDataKey(entry, dataKey);\n    if (layout === 'horizontal') {\n      return {\n        x: getCateCoordinateOfLine({\n          axis: xAxis,\n          ticks: xAxisTicks,\n          bandSize: bandSize,\n          entry: entry,\n          index: index\n        }),\n        y: isNil(value) ? null : yAxis.scale(value),\n        value: value,\n        payload: entry\n      };\n    }\n    return {\n      x: isNil(value) ? null : xAxis.scale(value),\n      y: getCateCoordinateOfLine({\n        axis: yAxis,\n        ticks: yAxisTicks,\n        bandSize: bandSize,\n        entry: entry,\n        index: index\n      }),\n      value: value,\n      payload: entry\n    };\n  });\n  return _objectSpread({\n    points: points,\n    layout: layout\n  }, offset);\n});"], "mappings": "AAAA,IAAIA,SAAS,GAAG,CAAC,MAAM,EAAE,QAAQ,EAAE,cAAc,EAAE,KAAK,CAAC;EACvDC,UAAU,GAAG,CAAC,KAAK,CAAC;AACtB,SAASC,OAAOA,CAACC,CAAC,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,CAAC,EAAE;IAAE,OAAO,OAAOA,CAAC;EAAE,CAAC,GAAG,UAAUA,CAAC,EAAE;IAAE,OAAOA,CAAC,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,CAAC,CAACG,WAAW,KAAKF,MAAM,IAAID,CAAC,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,CAAC;EAAE,CAAC,EAAED,OAAO,CAACC,CAAC,CAAC;AAAE;AAC7T,SAASK,wBAAwBA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EAAE,IAAID,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIE,MAAM,GAAGC,6BAA6B,CAACH,MAAM,EAAEC,QAAQ,CAAC;EAAE,IAAIG,GAAG,EAAEC,CAAC;EAAE,IAAIC,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIC,gBAAgB,GAAGF,MAAM,CAACC,qBAAqB,CAACP,MAAM,CAAC;IAAE,KAAKK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGG,gBAAgB,CAACC,MAAM,EAAEJ,CAAC,EAAE,EAAE;MAAED,GAAG,GAAGI,gBAAgB,CAACH,CAAC,CAAC;MAAE,IAAIJ,QAAQ,CAACS,OAAO,CAACN,GAAG,CAAC,IAAI,CAAC,EAAE;MAAU,IAAI,CAACE,MAAM,CAACR,SAAS,CAACa,oBAAoB,CAACC,IAAI,CAACZ,MAAM,EAAEI,GAAG,CAAC,EAAE;MAAUF,MAAM,CAACE,GAAG,CAAC,GAAGJ,MAAM,CAACI,GAAG,CAAC;IAAE;EAAE;EAAE,OAAOF,MAAM;AAAE;AAC3e,SAASC,6BAA6BA,CAACH,MAAM,EAAEC,QAAQ,EAAE;EAAE,IAAID,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIE,MAAM,GAAG,CAAC,CAAC;EAAE,KAAK,IAAIE,GAAG,IAAIJ,MAAM,EAAE;IAAE,IAAIM,MAAM,CAACR,SAAS,CAACe,cAAc,CAACD,IAAI,CAACZ,MAAM,EAAEI,GAAG,CAAC,EAAE;MAAE,IAAIH,QAAQ,CAACS,OAAO,CAACN,GAAG,CAAC,IAAI,CAAC,EAAE;MAAUF,MAAM,CAACE,GAAG,CAAC,GAAGJ,MAAM,CAACI,GAAG,CAAC;IAAE;EAAE;EAAE,OAAOF,MAAM;AAAE;AACtR,SAASY,QAAQA,CAAA,EAAG;EAAEA,QAAQ,GAAGR,MAAM,CAACS,MAAM,GAAGT,MAAM,CAACS,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUd,MAAM,EAAE;IAAE,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,SAAS,CAACR,MAAM,EAAEJ,CAAC,EAAE,EAAE;MAAE,IAAIL,MAAM,GAAGiB,SAAS,CAACZ,CAAC,CAAC;MAAE,KAAK,IAAID,GAAG,IAAIJ,MAAM,EAAE;QAAE,IAAIM,MAAM,CAACR,SAAS,CAACe,cAAc,CAACD,IAAI,CAACZ,MAAM,EAAEI,GAAG,CAAC,EAAE;UAAEF,MAAM,CAACE,GAAG,CAAC,GAAGJ,MAAM,CAACI,GAAG,CAAC;QAAE;MAAE;IAAE;IAAE,OAAOF,MAAM;EAAE,CAAC;EAAE,OAAOY,QAAQ,CAACI,KAAK,CAAC,IAAI,EAAED,SAAS,CAAC;AAAE;AAClV,SAASE,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAIC,CAAC,GAAGhB,MAAM,CAACiB,IAAI,CAACH,CAAC,CAAC;EAAE,IAAId,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIb,CAAC,GAAGY,MAAM,CAACC,qBAAqB,CAACa,CAAC,CAAC;IAAEC,CAAC,KAAK3B,CAAC,GAAGA,CAAC,CAAC8B,MAAM,CAAC,UAAUH,CAAC,EAAE;MAAE,OAAOf,MAAM,CAACmB,wBAAwB,CAACL,CAAC,EAAEC,CAAC,CAAC,CAACK,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEJ,CAAC,CAACK,IAAI,CAACT,KAAK,CAACI,CAAC,EAAE5B,CAAC,CAAC;EAAE;EAAE,OAAO4B,CAAC;AAAE;AAC9P,SAASM,aAAaA,CAACR,CAAC,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,SAAS,CAACR,MAAM,EAAEY,CAAC,EAAE,EAAE;IAAE,IAAIC,CAAC,GAAG,IAAI,IAAIL,SAAS,CAACI,CAAC,CAAC,GAAGJ,SAAS,CAACI,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGF,OAAO,CAACb,MAAM,CAACgB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACO,OAAO,CAAC,UAAUR,CAAC,EAAE;MAAES,eAAe,CAACV,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAACD,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGf,MAAM,CAACyB,yBAAyB,GAAGzB,MAAM,CAAC0B,gBAAgB,CAACZ,CAAC,EAAEd,MAAM,CAACyB,yBAAyB,CAACT,CAAC,CAAC,CAAC,GAAGH,OAAO,CAACb,MAAM,CAACgB,CAAC,CAAC,CAAC,CAACO,OAAO,CAAC,UAAUR,CAAC,EAAE;MAAEf,MAAM,CAAC2B,cAAc,CAACb,CAAC,EAAEC,CAAC,EAAEf,MAAM,CAACmB,wBAAwB,CAACH,CAAC,EAAED,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AACtb,SAASc,kBAAkBA,CAACC,GAAG,EAAE;EAAE,OAAOC,kBAAkB,CAACD,GAAG,CAAC,IAAIE,gBAAgB,CAACF,GAAG,CAAC,IAAIG,2BAA2B,CAACH,GAAG,CAAC,IAAII,kBAAkB,CAAC,CAAC;AAAE;AACxJ,SAASA,kBAAkBA,CAAA,EAAG;EAAE,MAAM,IAAIC,SAAS,CAAC,sIAAsI,CAAC;AAAE;AAC7L,SAASF,2BAA2BA,CAAC5C,CAAC,EAAE+C,MAAM,EAAE;EAAE,IAAI,CAAC/C,CAAC,EAAE;EAAQ,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE,OAAOgD,iBAAiB,CAAChD,CAAC,EAAE+C,MAAM,CAAC;EAAE,IAAIE,CAAC,GAAGrC,MAAM,CAACR,SAAS,CAAC8C,QAAQ,CAAChC,IAAI,CAAClB,CAAC,CAAC,CAACmD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAAE,IAAIF,CAAC,KAAK,QAAQ,IAAIjD,CAAC,CAACG,WAAW,EAAE8C,CAAC,GAAGjD,CAAC,CAACG,WAAW,CAACiD,IAAI;EAAE,IAAIH,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,KAAK,EAAE,OAAOI,KAAK,CAACC,IAAI,CAACtD,CAAC,CAAC;EAAE,IAAIiD,CAAC,KAAK,WAAW,IAAI,0CAA0C,CAACM,IAAI,CAACN,CAAC,CAAC,EAAE,OAAOD,iBAAiB,CAAChD,CAAC,EAAE+C,MAAM,CAAC;AAAE;AAC/Z,SAASJ,gBAAgBA,CAACa,IAAI,EAAE;EAAE,IAAI,OAAOvD,MAAM,KAAK,WAAW,IAAIuD,IAAI,CAACvD,MAAM,CAACC,QAAQ,CAAC,IAAI,IAAI,IAAIsD,IAAI,CAAC,YAAY,CAAC,IAAI,IAAI,EAAE,OAAOH,KAAK,CAACC,IAAI,CAACE,IAAI,CAAC;AAAE;AAC7J,SAASd,kBAAkBA,CAACD,GAAG,EAAE;EAAE,IAAIY,KAAK,CAACI,OAAO,CAAChB,GAAG,CAAC,EAAE,OAAOO,iBAAiB,CAACP,GAAG,CAAC;AAAE;AAC1F,SAASO,iBAAiBA,CAACP,GAAG,EAAEiB,GAAG,EAAE;EAAE,IAAIA,GAAG,IAAI,IAAI,IAAIA,GAAG,GAAGjB,GAAG,CAAC1B,MAAM,EAAE2C,GAAG,GAAGjB,GAAG,CAAC1B,MAAM;EAAE,KAAK,IAAIJ,CAAC,GAAG,CAAC,EAAEgD,IAAI,GAAG,IAAIN,KAAK,CAACK,GAAG,CAAC,EAAE/C,CAAC,GAAG+C,GAAG,EAAE/C,CAAC,EAAE,EAAEgD,IAAI,CAAChD,CAAC,CAAC,GAAG8B,GAAG,CAAC9B,CAAC,CAAC;EAAE,OAAOgD,IAAI;AAAE;AAClL,SAASC,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAAE,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIhB,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AACxJ,SAASiB,iBAAiBA,CAACvD,MAAM,EAAEwD,KAAK,EAAE;EAAE,KAAK,IAAIrD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqD,KAAK,CAACjD,MAAM,EAAEJ,CAAC,EAAE,EAAE;IAAE,IAAIsD,UAAU,GAAGD,KAAK,CAACrD,CAAC,CAAC;IAAEsD,UAAU,CAACjC,UAAU,GAAGiC,UAAU,CAACjC,UAAU,IAAI,KAAK;IAAEiC,UAAU,CAACC,YAAY,GAAG,IAAI;IAAE,IAAI,OAAO,IAAID,UAAU,EAAEA,UAAU,CAACE,QAAQ,GAAG,IAAI;IAAEvD,MAAM,CAAC2B,cAAc,CAAC/B,MAAM,EAAE4D,cAAc,CAACH,UAAU,CAACvD,GAAG,CAAC,EAAEuD,UAAU,CAAC;EAAE;AAAE;AAC5U,SAASI,YAAYA,CAACP,WAAW,EAAEQ,UAAU,EAAEC,WAAW,EAAE;EAAE,IAAID,UAAU,EAAEP,iBAAiB,CAACD,WAAW,CAAC1D,SAAS,EAAEkE,UAAU,CAAC;EAAE,IAAIC,WAAW,EAAER,iBAAiB,CAACD,WAAW,EAAES,WAAW,CAAC;EAAE3D,MAAM,CAAC2B,cAAc,CAACuB,WAAW,EAAE,WAAW,EAAE;IAAEK,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,OAAOL,WAAW;AAAE;AAC5R,SAASU,UAAUA,CAAC5C,CAAC,EAAE5B,CAAC,EAAE0B,CAAC,EAAE;EAAE,OAAO1B,CAAC,GAAGyE,eAAe,CAACzE,CAAC,CAAC,EAAE0E,0BAA0B,CAAC9C,CAAC,EAAE+C,yBAAyB,CAAC,CAAC,GAAGC,OAAO,CAACC,SAAS,CAAC7E,CAAC,EAAE0B,CAAC,IAAI,EAAE,EAAE+C,eAAe,CAAC7C,CAAC,CAAC,CAACzB,WAAW,CAAC,GAAGH,CAAC,CAACwB,KAAK,CAACI,CAAC,EAAEF,CAAC,CAAC,CAAC;AAAE;AAC1M,SAASgD,0BAA0BA,CAACI,IAAI,EAAE5D,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAKnB,OAAO,CAACmB,IAAI,CAAC,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,CAAC,EAAE;IAAE,OAAOA,IAAI;EAAE,CAAC,MAAM,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAI4B,SAAS,CAAC,0DAA0D,CAAC;EAAE;EAAE,OAAOiC,sBAAsB,CAACD,IAAI,CAAC;AAAE;AAC/R,SAASC,sBAAsBA,CAACD,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAIE,cAAc,CAAC,2DAA2D,CAAC;EAAE;EAAE,OAAOF,IAAI;AAAE;AACrK,SAASH,yBAAyBA,CAAA,EAAG;EAAE,IAAI;IAAE,IAAI/C,CAAC,GAAG,CAACqD,OAAO,CAAC7E,SAAS,CAAC8E,OAAO,CAAChE,IAAI,CAAC0D,OAAO,CAACC,SAAS,CAACI,OAAO,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;EAAE,CAAC,CAAC,OAAOrD,CAAC,EAAE,CAAC;EAAE,OAAO,CAAC+C,yBAAyB,GAAG,SAASA,yBAAyBA,CAAA,EAAG;IAAE,OAAO,CAAC,CAAC/C,CAAC;EAAE,CAAC,EAAE,CAAC;AAAE;AAClP,SAAS6C,eAAeA,CAACzE,CAAC,EAAE;EAAEyE,eAAe,GAAG7D,MAAM,CAACuE,cAAc,GAAGvE,MAAM,CAACwE,cAAc,CAAC9D,IAAI,CAAC,CAAC,GAAG,SAASmD,eAAeA,CAACzE,CAAC,EAAE;IAAE,OAAOA,CAAC,CAACqF,SAAS,IAAIzE,MAAM,CAACwE,cAAc,CAACpF,CAAC,CAAC;EAAE,CAAC;EAAE,OAAOyE,eAAe,CAACzE,CAAC,CAAC;AAAE;AACnN,SAASsF,SAASA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EAAE,IAAI,OAAOA,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,IAAI,EAAE;IAAE,MAAM,IAAI1C,SAAS,CAAC,oDAAoD,CAAC;EAAE;EAAEyC,QAAQ,CAACnF,SAAS,GAAGQ,MAAM,CAAC6E,MAAM,CAACD,UAAU,IAAIA,UAAU,CAACpF,SAAS,EAAE;IAAED,WAAW,EAAE;MAAEuF,KAAK,EAAEH,QAAQ;MAAEpB,QAAQ,EAAE,IAAI;MAAED,YAAY,EAAE;IAAK;EAAE,CAAC,CAAC;EAAEtD,MAAM,CAAC2B,cAAc,CAACgD,QAAQ,EAAE,WAAW,EAAE;IAAEpB,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,IAAIqB,UAAU,EAAEG,eAAe,CAACJ,QAAQ,EAAEC,UAAU,CAAC;AAAE;AACnc,SAASG,eAAeA,CAAC3F,CAAC,EAAE4F,CAAC,EAAE;EAAED,eAAe,GAAG/E,MAAM,CAACuE,cAAc,GAAGvE,MAAM,CAACuE,cAAc,CAAC7D,IAAI,CAAC,CAAC,GAAG,SAASqE,eAAeA,CAAC3F,CAAC,EAAE4F,CAAC,EAAE;IAAE5F,CAAC,CAACqF,SAAS,GAAGO,CAAC;IAAE,OAAO5F,CAAC;EAAE,CAAC;EAAE,OAAO2F,eAAe,CAAC3F,CAAC,EAAE4F,CAAC,CAAC;AAAE;AACvM,SAASxD,eAAeA,CAACyD,GAAG,EAAEnF,GAAG,EAAEgF,KAAK,EAAE;EAAEhF,GAAG,GAAG0D,cAAc,CAAC1D,GAAG,CAAC;EAAE,IAAIA,GAAG,IAAImF,GAAG,EAAE;IAAEjF,MAAM,CAAC2B,cAAc,CAACsD,GAAG,EAAEnF,GAAG,EAAE;MAAEgF,KAAK,EAAEA,KAAK;MAAE1D,UAAU,EAAE,IAAI;MAAEkC,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAE0B,GAAG,CAACnF,GAAG,CAAC,GAAGgF,KAAK;EAAE;EAAE,OAAOG,GAAG;AAAE;AAC3O,SAASzB,cAAcA,CAACxC,CAAC,EAAE;EAAE,IAAIjB,CAAC,GAAGmF,YAAY,CAAClE,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAI7B,OAAO,CAACY,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC5G,SAASmF,YAAYA,CAAClE,CAAC,EAAED,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAI5B,OAAO,CAAC6B,CAAC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIF,CAAC,GAAGE,CAAC,CAAC3B,MAAM,CAAC8F,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAKrE,CAAC,EAAE;IAAE,IAAIf,CAAC,GAAGe,CAAC,CAACR,IAAI,CAACU,CAAC,EAAED,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAI5B,OAAO,CAACY,CAAC,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAImC,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAKnB,CAAC,GAAGqE,MAAM,GAAGC,MAAM,EAAErE,CAAC,CAAC;AAAE;AAC3T;AACA;AACA;AACA,OAAOsE,KAAK,IAAIC,aAAa,QAAQ,OAAO;AAC5C,OAAOC,OAAO,MAAM,cAAc;AAClC,OAAOC,UAAU,MAAM,mBAAmB;AAC1C,OAAOC,KAAK,MAAM,cAAc;AAChC,OAAOC,OAAO,MAAM,gBAAgB;AACpC,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,GAAG,QAAQ,cAAc;AAClC,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,QAAQ,QAAQ,YAAY;AACrC,SAASC,QAAQ,EAAEC,iBAAiB,QAAQ,mBAAmB;AAC/D,SAASC,aAAa,EAAEC,WAAW,EAAEC,UAAU,QAAQ,oBAAoB;AAC3E,SAASC,MAAM,QAAQ,gBAAgB;AACvC,SAASC,uBAAuB,EAAEC,iBAAiB,QAAQ,oBAAoB;AAC/E,OAAO,IAAIC,IAAI,GAAG,aAAa,UAAUC,cAAc,EAAE;EACvD,SAASD,IAAIA,CAAA,EAAG;IACd,IAAIE,KAAK;IACT5D,eAAe,CAAC,IAAI,EAAE0D,IAAI,CAAC;IAC3B,KAAK,IAAIG,IAAI,GAAGlG,SAAS,CAACR,MAAM,EAAE2G,IAAI,GAAG,IAAIrE,KAAK,CAACoE,IAAI,CAAC,EAAEE,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGF,IAAI,EAAEE,IAAI,EAAE,EAAE;MACvFD,IAAI,CAACC,IAAI,CAAC,GAAGpG,SAAS,CAACoG,IAAI,CAAC;IAC9B;IACAH,KAAK,GAAGhD,UAAU,CAAC,IAAI,EAAE8C,IAAI,EAAE,EAAE,CAACM,MAAM,CAACF,IAAI,CAAC,CAAC;IAC/CtF,eAAe,CAACoF,KAAK,EAAE,OAAO,EAAE;MAC9BK,mBAAmB,EAAE,IAAI;MACzBC,WAAW,EAAE;IACf,CAAC,CAAC;IACF1F,eAAe,CAACoF,KAAK,EAAE,+BAA+B,EAAE,UAAUM,WAAW,EAAE/G,MAAM,EAAE;MACrF,OAAO,EAAE,CAAC6G,MAAM,CAAC7G,MAAM,EAAE,KAAK,CAAC,CAAC6G,MAAM,CAACE,WAAW,GAAG/G,MAAM,EAAE,IAAI,CAAC;IACpE,CAAC,CAAC;IACFqB,eAAe,CAACoF,KAAK,EAAE,oBAAoB,EAAE,UAAUzG,MAAM,EAAE+G,WAAW,EAAEC,KAAK,EAAE;MACjF,IAAIC,UAAU,GAAGD,KAAK,CAACE,MAAM,CAAC,UAAUC,GAAG,EAAEC,IAAI,EAAE;QACjD,OAAOD,GAAG,GAAGC,IAAI;MACnB,CAAC,CAAC;;MAEF;MACA,IAAI,CAACH,UAAU,EAAE;QACf,OAAOR,KAAK,CAACY,6BAA6B,CAACN,WAAW,EAAE/G,MAAM,CAAC;MACjE;MACA,IAAIsH,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACxH,MAAM,GAAGiH,UAAU,CAAC;MAC3C,IAAIQ,YAAY,GAAGzH,MAAM,GAAGiH,UAAU;MACtC,IAAIS,UAAU,GAAGX,WAAW,GAAG/G,MAAM;MACrC,IAAI2H,WAAW,GAAG,EAAE;MACpB,KAAK,IAAI/H,CAAC,GAAG,CAAC,EAAEgI,GAAG,GAAG,CAAC,EAAEhI,CAAC,GAAGoH,KAAK,CAAChH,MAAM,EAAE4H,GAAG,IAAIZ,KAAK,CAACpH,CAAC,CAAC,EAAE,EAAEA,CAAC,EAAE;QAC/D,IAAIgI,GAAG,GAAGZ,KAAK,CAACpH,CAAC,CAAC,GAAG6H,YAAY,EAAE;UACjCE,WAAW,GAAG,EAAE,CAACd,MAAM,CAACpF,kBAAkB,CAACuF,KAAK,CAAC5E,KAAK,CAAC,CAAC,EAAExC,CAAC,CAAC,CAAC,EAAE,CAAC6H,YAAY,GAAGG,GAAG,CAAC,CAAC;UACpF;QACF;MACF;MACA,IAAIC,UAAU,GAAGF,WAAW,CAAC3H,MAAM,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE0H,UAAU,CAAC,GAAG,CAACA,UAAU,CAAC;MAC9E,OAAO,EAAE,CAACb,MAAM,CAACpF,kBAAkB,CAAC8E,IAAI,CAACuB,MAAM,CAACd,KAAK,EAAEM,KAAK,CAAC,CAAC,EAAE7F,kBAAkB,CAACkG,WAAW,CAAC,EAAEE,UAAU,CAAC,CAACE,GAAG,CAAC,UAAUC,IAAI,EAAE;QAC/H,OAAO,EAAE,CAACnB,MAAM,CAACmB,IAAI,EAAE,IAAI,CAAC;MAC9B,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;IACf,CAAC,CAAC;IACF5G,eAAe,CAACoF,KAAK,EAAE,IAAI,EAAEV,QAAQ,CAAC,gBAAgB,CAAC,CAAC;IACxD1E,eAAe,CAACoF,KAAK,EAAE,SAAS,EAAE,UAAUyB,IAAI,EAAE;MAChDzB,KAAK,CAAC0B,SAAS,GAAGD,IAAI;IACxB,CAAC,CAAC;IACF7G,eAAe,CAACoF,KAAK,EAAE,oBAAoB,EAAE,YAAY;MACvDA,KAAK,CAAC2B,QAAQ,CAAC;QACbtB,mBAAmB,EAAE;MACvB,CAAC,CAAC;MACF,IAAIL,KAAK,CAACxD,KAAK,CAACoF,cAAc,EAAE;QAC9B5B,KAAK,CAACxD,KAAK,CAACoF,cAAc,CAAC,CAAC;MAC9B;IACF,CAAC,CAAC;IACFhH,eAAe,CAACoF,KAAK,EAAE,sBAAsB,EAAE,YAAY;MACzDA,KAAK,CAAC2B,QAAQ,CAAC;QACbtB,mBAAmB,EAAE;MACvB,CAAC,CAAC;MACF,IAAIL,KAAK,CAACxD,KAAK,CAACqF,gBAAgB,EAAE;QAChC7B,KAAK,CAACxD,KAAK,CAACqF,gBAAgB,CAAC,CAAC;MAChC;IACF,CAAC,CAAC;IACF,OAAO7B,KAAK;EACd;EACAlC,SAAS,CAACgC,IAAI,EAAEC,cAAc,CAAC;EAC/B,OAAOlD,YAAY,CAACiD,IAAI,EAAE,CAAC;IACzB5G,GAAG,EAAE,mBAAmB;IACxBgF,KAAK,EAAE,SAAS4D,iBAAiBA,CAAA,EAAG;MAClC,IAAI,CAAC,IAAI,CAACtF,KAAK,CAACuF,iBAAiB,EAAE;QACjC;MACF;MACA,IAAIzB,WAAW,GAAG,IAAI,CAAC0B,cAAc,CAAC,CAAC;MACvC,IAAI,CAACL,QAAQ,CAAC;QACZrB,WAAW,EAAEA;MACf,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDpH,GAAG,EAAE,oBAAoB;IACzBgF,KAAK,EAAE,SAAS+D,kBAAkBA,CAAA,EAAG;MACnC,IAAI,CAAC,IAAI,CAACzF,KAAK,CAACuF,iBAAiB,EAAE;QACjC;MACF;MACA,IAAIzB,WAAW,GAAG,IAAI,CAAC0B,cAAc,CAAC,CAAC;MACvC,IAAI1B,WAAW,KAAK,IAAI,CAAC4B,KAAK,CAAC5B,WAAW,EAAE;QAC1C,IAAI,CAACqB,QAAQ,CAAC;UACZrB,WAAW,EAAEA;QACf,CAAC,CAAC;MACJ;IACF;EACF,CAAC,EAAE;IACDpH,GAAG,EAAE,gBAAgB;IACrBgF,KAAK,EAAE,SAAS8D,cAAcA,CAAA,EAAG;MAC/B,IAAIG,QAAQ,GAAG,IAAI,CAACT,SAAS;MAC7B,IAAI;QACF,OAAOS,QAAQ,IAAIA,QAAQ,CAACH,cAAc,IAAIG,QAAQ,CAACH,cAAc,CAAC,CAAC,IAAI,CAAC;MAC9E,CAAC,CAAC,OAAOI,GAAG,EAAE;QACZ,OAAO,CAAC;MACV;IACF;EACF,CAAC,EAAE;IACDlJ,GAAG,EAAE,gBAAgB;IACrBgF,KAAK,EAAE,SAASmE,cAAcA,CAACC,QAAQ,EAAEC,UAAU,EAAE;MACnD,IAAI,IAAI,CAAC/F,KAAK,CAACuF,iBAAiB,IAAI,CAAC,IAAI,CAACG,KAAK,CAAC7B,mBAAmB,EAAE;QACnE,OAAO,IAAI;MACb;MACA,IAAImC,WAAW,GAAG,IAAI,CAAChG,KAAK;QAC1BiG,MAAM,GAAGD,WAAW,CAACC,MAAM;QAC3BC,KAAK,GAAGF,WAAW,CAACE,KAAK;QACzBC,KAAK,GAAGH,WAAW,CAACG,KAAK;QACzBC,MAAM,GAAGJ,WAAW,CAACI,MAAM;QAC3BC,QAAQ,GAAGL,WAAW,CAACK,QAAQ;MACjC,IAAIC,aAAa,GAAGtD,aAAa,CAACqD,QAAQ,EAAExD,QAAQ,CAAC;MACrD,IAAI,CAACyD,aAAa,EAAE;QAClB,OAAO,IAAI;MACb;MACA,IAAIC,kBAAkB,GAAG,SAASA,kBAAkBA,CAACC,SAAS,EAAEC,OAAO,EAAE;QACvE,OAAO;UACLC,CAAC,EAAEF,SAAS,CAACE,CAAC;UACdC,CAAC,EAAEH,SAAS,CAACG,CAAC;UACdjF,KAAK,EAAE8E,SAAS,CAAC9E,KAAK;UACtBkF,QAAQ,EAAEvD,iBAAiB,CAACmD,SAAS,CAACK,OAAO,EAAEJ,OAAO;QACxD,CAAC;MACH,CAAC;MACD,IAAIK,aAAa,GAAG;QAClBC,QAAQ,EAAEjB,QAAQ,GAAG,gBAAgB,CAAClC,MAAM,CAACmC,UAAU,EAAE,GAAG,CAAC,GAAG;MAClE,CAAC;MACD,OAAO,aAAa7D,KAAK,CAAC8E,aAAa,CAACrE,KAAK,EAAEmE,aAAa,EAAER,aAAa,CAACxB,GAAG,CAAC,UAAUmC,IAAI,EAAE;QAC9F,OAAO,aAAa/E,KAAK,CAACgF,YAAY,CAACD,IAAI,EAAE;UAC3CvK,GAAG,EAAE,MAAM,CAACkH,MAAM,CAACqD,IAAI,CAACjH,KAAK,CAACyG,OAAO,CAAC;UACtCU,IAAI,EAAElB,MAAM;UACZC,KAAK,EAAEA,KAAK;UACZC,KAAK,EAAEA,KAAK;UACZC,MAAM,EAAEA,MAAM;UACdG,kBAAkB,EAAEA;QACtB,CAAC,CAAC;MACJ,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE;IACD7J,GAAG,EAAE,YAAY;IACjBgF,KAAK,EAAE,SAAS0F,UAAUA,CAACtB,QAAQ,EAAEuB,OAAO,EAAEtB,UAAU,EAAE;MACxD,IAAIR,iBAAiB,GAAG,IAAI,CAACvF,KAAK,CAACuF,iBAAiB;MACpD,IAAIA,iBAAiB,IAAI,CAAC,IAAI,CAACG,KAAK,CAAC7B,mBAAmB,EAAE;QACxD,OAAO,IAAI;MACb;MACA,IAAIyD,YAAY,GAAG,IAAI,CAACtH,KAAK;QAC3BuH,GAAG,GAAGD,YAAY,CAACC,GAAG;QACtBtB,MAAM,GAAGqB,YAAY,CAACrB,MAAM;QAC5BQ,OAAO,GAAGa,YAAY,CAACb,OAAO;MAChC,IAAIe,SAAS,GAAGvE,WAAW,CAAC,IAAI,CAACjD,KAAK,EAAE,KAAK,CAAC;MAC9C,IAAIyH,cAAc,GAAGxE,WAAW,CAACsE,GAAG,EAAE,IAAI,CAAC;MAC3C,IAAIG,IAAI,GAAGzB,MAAM,CAACnB,GAAG,CAAC,UAAU6C,KAAK,EAAEhL,CAAC,EAAE;QACxC,IAAIiL,QAAQ,GAAG1J,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC;UACvDxB,GAAG,EAAE,MAAM,CAACkH,MAAM,CAACjH,CAAC,CAAC;UACrBgB,CAAC,EAAE;QACL,CAAC,EAAE6J,SAAS,CAAC,EAAEC,cAAc,CAAC,EAAE,CAAC,CAAC,EAAE;UAClCI,KAAK,EAAElL,CAAC;UACRmL,EAAE,EAAEH,KAAK,CAACjB,CAAC;UACXqB,EAAE,EAAEJ,KAAK,CAAChB,CAAC;UACXjF,KAAK,EAAEiG,KAAK,CAACjG,KAAK;UAClB+E,OAAO,EAAEA,OAAO;UAChBI,OAAO,EAAEc,KAAK,CAACd,OAAO;UACtBZ,MAAM,EAAEA;QACV,CAAC,CAAC;QACF,OAAO3C,IAAI,CAAC0E,aAAa,CAACT,GAAG,EAAEK,QAAQ,CAAC;MAC1C,CAAC,CAAC;MACF,IAAIK,SAAS,GAAG;QACdlB,QAAQ,EAAEjB,QAAQ,GAAG,gBAAgB,CAAClC,MAAM,CAACyD,OAAO,GAAG,EAAE,GAAG,OAAO,CAAC,CAACzD,MAAM,CAACmC,UAAU,EAAE,GAAG,CAAC,GAAG;MACjG,CAAC;MACD,OAAO,aAAa7D,KAAK,CAAC8E,aAAa,CAACrE,KAAK,EAAEvF,QAAQ,CAAC;QACtD8K,SAAS,EAAE,oBAAoB;QAC/BxL,GAAG,EAAE;MACP,CAAC,EAAEuL,SAAS,CAAC,EAAEP,IAAI,CAAC;IACtB;EACF,CAAC,EAAE;IACDhL,GAAG,EAAE,uBAAuB;IAC5BgF,KAAK,EAAE,SAASyG,qBAAqBA,CAAClC,MAAM,EAAEH,QAAQ,EAAEC,UAAU,EAAE/F,KAAK,EAAE;MACzE,IAAIoI,YAAY,GAAG,IAAI,CAACpI,KAAK;QAC3BqI,IAAI,GAAGD,YAAY,CAACC,IAAI;QACxBjC,MAAM,GAAGgC,YAAY,CAAChC,MAAM;QAC5BkC,YAAY,GAAGF,YAAY,CAACE,YAAY;QACxCC,GAAG,GAAGH,YAAY,CAACG,GAAG;QACtBC,MAAM,GAAGnM,wBAAwB,CAAC+L,YAAY,EAAEvM,SAAS,CAAC;MAC5D,IAAI4M,UAAU,GAAGvK,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE+E,WAAW,CAACuF,MAAM,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;QAC7FE,IAAI,EAAE,MAAM;QACZR,SAAS,EAAE,qBAAqB;QAChCnB,QAAQ,EAAEjB,QAAQ,GAAG,gBAAgB,CAAClC,MAAM,CAACmC,UAAU,EAAE,GAAG,CAAC,GAAG,IAAI;QACpEE,MAAM,EAAEA;MACV,CAAC,EAAEjG,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QACbqI,IAAI,EAAEA,IAAI;QACVjC,MAAM,EAAEA,MAAM;QACdkC,YAAY,EAAEA;MAChB,CAAC,CAAC;MACF,OAAO,aAAapG,KAAK,CAAC8E,aAAa,CAACvE,KAAK,EAAErF,QAAQ,CAAC,CAAC,CAAC,EAAEqL,UAAU,EAAE;QACtEE,OAAO,EAAE,IAAI,CAACA;MAChB,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE;IACDjM,GAAG,EAAE,0BAA0B;IAC/BgF,KAAK,EAAE,SAASkH,wBAAwBA,CAAC9C,QAAQ,EAAEC,UAAU,EAAE;MAC7D,IAAI8C,MAAM,GAAG,IAAI;MACjB,IAAIC,YAAY,GAAG,IAAI,CAAC9I,KAAK;QAC3BiG,MAAM,GAAG6C,YAAY,CAAC7C,MAAM;QAC5B8C,eAAe,GAAGD,YAAY,CAACC,eAAe;QAC9CxD,iBAAiB,GAAGuD,YAAY,CAACvD,iBAAiB;QAClDyD,cAAc,GAAGF,YAAY,CAACE,cAAc;QAC5CC,iBAAiB,GAAGH,YAAY,CAACG,iBAAiB;QAClDC,eAAe,GAAGJ,YAAY,CAACI,eAAe;QAC9CC,WAAW,GAAGL,YAAY,CAACK,WAAW;QACtCC,gBAAgB,GAAGN,YAAY,CAACM,gBAAgB;QAChDC,KAAK,GAAGP,YAAY,CAACO,KAAK;QAC1BC,MAAM,GAAGR,YAAY,CAACQ,MAAM;MAC9B,IAAIC,WAAW,GAAG,IAAI,CAAC7D,KAAK;QAC1B8D,UAAU,GAAGD,WAAW,CAACC,UAAU;QACnC1F,WAAW,GAAGyF,WAAW,CAACzF,WAAW;MACvC,OAAO,aAAa5B,KAAK,CAAC8E,aAAa,CAAC5E,OAAO,EAAE;QAC/CqH,KAAK,EAAET,cAAc;QACrBU,QAAQ,EAAET,iBAAiB;QAC3BU,QAAQ,EAAEpE,iBAAiB;QAC3BqE,MAAM,EAAEV,eAAe;QACvB5J,IAAI,EAAE;UACJ1B,CAAC,EAAE;QACL,CAAC;QACDiM,EAAE,EAAE;UACFjM,CAAC,EAAE;QACL,CAAC;QACDlB,GAAG,EAAE,OAAO,CAACkH,MAAM,CAACuF,WAAW,CAAC;QAChC/D,cAAc,EAAE,IAAI,CAAC0E,kBAAkB;QACvCzE,gBAAgB,EAAE,IAAI,CAAC0E;MACzB,CAAC,EAAE,UAAUC,IAAI,EAAE;QACjB,IAAIpM,CAAC,GAAGoM,IAAI,CAACpM,CAAC;QACd,IAAI4L,UAAU,EAAE;UACd,IAAIS,oBAAoB,GAAGT,UAAU,CAACzM,MAAM,GAAGkJ,MAAM,CAAClJ,MAAM;UAC5D,IAAImN,QAAQ,GAAGjE,MAAM,CAACnB,GAAG,CAAC,UAAU6C,KAAK,EAAEE,KAAK,EAAE;YAChD,IAAIsC,cAAc,GAAG7F,IAAI,CAACC,KAAK,CAACsD,KAAK,GAAGoC,oBAAoB,CAAC;YAC7D,IAAIT,UAAU,CAACW,cAAc,CAAC,EAAE;cAC9B,IAAIC,IAAI,GAAGZ,UAAU,CAACW,cAAc,CAAC;cACrC,IAAIE,aAAa,GAAGtH,iBAAiB,CAACqH,IAAI,CAAC1D,CAAC,EAAEiB,KAAK,CAACjB,CAAC,CAAC;cACtD,IAAI4D,aAAa,GAAGvH,iBAAiB,CAACqH,IAAI,CAACzD,CAAC,EAAEgB,KAAK,CAAChB,CAAC,CAAC;cACtD,OAAOzI,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEyJ,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;gBACjDjB,CAAC,EAAE2D,aAAa,CAACzM,CAAC,CAAC;gBACnB+I,CAAC,EAAE2D,aAAa,CAAC1M,CAAC;cACpB,CAAC,CAAC;YACJ;;YAEA;YACA,IAAIwL,gBAAgB,EAAE;cACpB,IAAImB,cAAc,GAAGxH,iBAAiB,CAACsG,KAAK,GAAG,CAAC,EAAE1B,KAAK,CAACjB,CAAC,CAAC;cAC1D,IAAI8D,cAAc,GAAGzH,iBAAiB,CAACuG,MAAM,GAAG,CAAC,EAAE3B,KAAK,CAAChB,CAAC,CAAC;cAC3D,OAAOzI,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEyJ,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;gBACjDjB,CAAC,EAAE6D,cAAc,CAAC3M,CAAC,CAAC;gBACpB+I,CAAC,EAAE6D,cAAc,CAAC5M,CAAC;cACrB,CAAC,CAAC;YACJ;YACA,OAAOM,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEyJ,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;cACjDjB,CAAC,EAAEiB,KAAK,CAACjB,CAAC;cACVC,CAAC,EAAEgB,KAAK,CAAChB;YACX,CAAC,CAAC;UACJ,CAAC,CAAC;UACF,OAAOkC,MAAM,CAACV,qBAAqB,CAAC+B,QAAQ,EAAEpE,QAAQ,EAAEC,UAAU,CAAC;QACrE;QACA,IAAI0E,YAAY,GAAG1H,iBAAiB,CAAC,CAAC,EAAEe,WAAW,CAAC;QACpD,IAAI4G,SAAS,GAAGD,YAAY,CAAC7M,CAAC,CAAC;QAC/B,IAAI+M,sBAAsB;QAC1B,IAAI5B,eAAe,EAAE;UACnB,IAAIhF,KAAK,GAAG,EAAE,CAACH,MAAM,CAACmF,eAAe,CAAC,CAAC6B,KAAK,CAAC,WAAW,CAAC,CAAC9F,GAAG,CAAC,UAAU+F,GAAG,EAAE;YAC3E,OAAOC,UAAU,CAACD,GAAG,CAAC;UACxB,CAAC,CAAC;UACFF,sBAAsB,GAAG9B,MAAM,CAACkC,kBAAkB,CAACL,SAAS,EAAE5G,WAAW,EAAEC,KAAK,CAAC;QACnF,CAAC,MAAM;UACL4G,sBAAsB,GAAG9B,MAAM,CAACzE,6BAA6B,CAACN,WAAW,EAAE4G,SAAS,CAAC;QACvF;QACA,OAAO7B,MAAM,CAACV,qBAAqB,CAAClC,MAAM,EAAEH,QAAQ,EAAEC,UAAU,EAAE;UAChEgD,eAAe,EAAE4B;QACnB,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDjO,GAAG,EAAE,aAAa;IAClBgF,KAAK,EAAE,SAASsJ,WAAWA,CAAClF,QAAQ,EAAEC,UAAU,EAAE;MAChD,IAAIkF,YAAY,GAAG,IAAI,CAACjL,KAAK;QAC3BiG,MAAM,GAAGgF,YAAY,CAAChF,MAAM;QAC5BV,iBAAiB,GAAG0F,YAAY,CAAC1F,iBAAiB;MACpD,IAAI2F,YAAY,GAAG,IAAI,CAACxF,KAAK;QAC3B8D,UAAU,GAAG0B,YAAY,CAAC1B,UAAU;QACpC1F,WAAW,GAAGoH,YAAY,CAACpH,WAAW;MACxC,IAAIyB,iBAAiB,IAAIU,MAAM,IAAIA,MAAM,CAAClJ,MAAM,KAAK,CAACyM,UAAU,IAAI1F,WAAW,GAAG,CAAC,IAAI,CAACvB,OAAO,CAACiH,UAAU,EAAEvD,MAAM,CAAC,CAAC,EAAE;QACpH,OAAO,IAAI,CAAC2C,wBAAwB,CAAC9C,QAAQ,EAAEC,UAAU,CAAC;MAC5D;MACA,OAAO,IAAI,CAACoC,qBAAqB,CAAClC,MAAM,EAAEH,QAAQ,EAAEC,UAAU,CAAC;IACjE;EACF,CAAC,EAAE;IACDrJ,GAAG,EAAE,QAAQ;IACbgF,KAAK,EAAE,SAASyJ,MAAMA,CAAA,EAAG;MACvB,IAAIC,YAAY;MAChB,IAAIC,YAAY,GAAG,IAAI,CAACrL,KAAK;QAC3BsL,IAAI,GAAGD,YAAY,CAACC,IAAI;QACxB/D,GAAG,GAAG8D,YAAY,CAAC9D,GAAG;QACtBtB,MAAM,GAAGoF,YAAY,CAACpF,MAAM;QAC5BiC,SAAS,GAAGmD,YAAY,CAACnD,SAAS;QAClChC,KAAK,GAAGmF,YAAY,CAACnF,KAAK;QAC1BC,KAAK,GAAGkF,YAAY,CAAClF,KAAK;QAC1BoF,GAAG,GAAGF,YAAY,CAACE,GAAG;QACtBC,IAAI,GAAGH,YAAY,CAACG,IAAI;QACxBnC,KAAK,GAAGgC,YAAY,CAAChC,KAAK;QAC1BC,MAAM,GAAG+B,YAAY,CAAC/B,MAAM;QAC5B/D,iBAAiB,GAAG8F,YAAY,CAAC9F,iBAAiB;QAClDkG,EAAE,GAAGJ,YAAY,CAACI,EAAE;MACtB,IAAIH,IAAI,IAAI,CAACrF,MAAM,IAAI,CAACA,MAAM,CAAClJ,MAAM,EAAE;QACrC,OAAO,IAAI;MACb;MACA,IAAI8G,mBAAmB,GAAG,IAAI,CAAC6B,KAAK,CAAC7B,mBAAmB;MACxD,IAAI6H,cAAc,GAAGzF,MAAM,CAAClJ,MAAM,KAAK,CAAC;MACxC,IAAI4O,UAAU,GAAGnJ,IAAI,CAAC,eAAe,EAAE0F,SAAS,CAAC;MACjD,IAAI0D,SAAS,GAAG1F,KAAK,IAAIA,KAAK,CAAC2F,iBAAiB;MAChD,IAAIC,SAAS,GAAG3F,KAAK,IAAIA,KAAK,CAAC0F,iBAAiB;MAChD,IAAI/F,QAAQ,GAAG8F,SAAS,IAAIE,SAAS;MACrC,IAAI/F,UAAU,GAAGzD,KAAK,CAACmJ,EAAE,CAAC,GAAG,IAAI,CAACA,EAAE,GAAGA,EAAE;MACzC,IAAIM,KAAK,GAAG,CAACX,YAAY,GAAGnI,WAAW,CAACsE,GAAG,EAAE,KAAK,CAAC,MAAM,IAAI,IAAI6D,YAAY,KAAK,KAAK,CAAC,GAAGA,YAAY,GAAG;UACtGzN,CAAC,EAAE,CAAC;UACJqO,WAAW,EAAE;QACf,CAAC;QACDC,OAAO,GAAGF,KAAK,CAACpO,CAAC;QACjBA,CAAC,GAAGsO,OAAO,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,OAAO;QACpCC,iBAAiB,GAAGH,KAAK,CAACC,WAAW;QACrCA,WAAW,GAAGE,iBAAiB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,iBAAiB;MACpE,IAAIC,KAAK,GAAGjJ,UAAU,CAACqE,GAAG,CAAC,GAAGA,GAAG,GAAG,CAAC,CAAC;QACpC6E,aAAa,GAAGD,KAAK,CAAC9E,OAAO;QAC7BA,OAAO,GAAG+E,aAAa,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,aAAa;MAC3D,IAAIC,OAAO,GAAG1O,CAAC,GAAG,CAAC,GAAGqO,WAAW;MACjC,OAAO,aAAa9J,KAAK,CAAC8E,aAAa,CAACrE,KAAK,EAAE;QAC7CuF,SAAS,EAAEyD;MACb,CAAC,EAAEC,SAAS,IAAIE,SAAS,GAAG,aAAa5J,KAAK,CAAC8E,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,aAAa9E,KAAK,CAAC8E,aAAa,CAAC,UAAU,EAAE;QACtHyE,EAAE,EAAE,WAAW,CAAC7H,MAAM,CAACmC,UAAU;MACnC,CAAC,EAAE,aAAa7D,KAAK,CAAC8E,aAAa,CAAC,MAAM,EAAE;QAC1CN,CAAC,EAAEkF,SAAS,GAAGJ,IAAI,GAAGA,IAAI,GAAGnC,KAAK,GAAG,CAAC;QACtC1C,CAAC,EAAEmF,SAAS,GAAGP,GAAG,GAAGA,GAAG,GAAGjC,MAAM,GAAG,CAAC;QACrCD,KAAK,EAAEuC,SAAS,GAAGvC,KAAK,GAAGA,KAAK,GAAG,CAAC;QACpCC,MAAM,EAAEwC,SAAS,GAAGxC,MAAM,GAAGA,MAAM,GAAG;MACxC,CAAC,CAAC,CAAC,EAAE,CAACjC,OAAO,IAAI,aAAanF,KAAK,CAAC8E,aAAa,CAAC,UAAU,EAAE;QAC5DyE,EAAE,EAAE,gBAAgB,CAAC7H,MAAM,CAACmC,UAAU;MACxC,CAAC,EAAE,aAAa7D,KAAK,CAAC8E,aAAa,CAAC,MAAM,EAAE;QAC1CN,CAAC,EAAE8E,IAAI,GAAGa,OAAO,GAAG,CAAC;QACrB1F,CAAC,EAAE4E,GAAG,GAAGc,OAAO,GAAG,CAAC;QACpBhD,KAAK,EAAEA,KAAK,GAAGgD,OAAO;QACtB/C,MAAM,EAAEA,MAAM,GAAG+C;MACnB,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,EAAE,CAACX,cAAc,IAAI,IAAI,CAACV,WAAW,CAAClF,QAAQ,EAAEC,UAAU,CAAC,EAAE,IAAI,CAACF,cAAc,CAACC,QAAQ,EAAEC,UAAU,CAAC,EAAE,CAAC2F,cAAc,IAAInE,GAAG,KAAK,IAAI,CAACH,UAAU,CAACtB,QAAQ,EAAEuB,OAAO,EAAEtB,UAAU,CAAC,EAAE,CAAC,CAACR,iBAAiB,IAAI1B,mBAAmB,KAAKjB,SAAS,CAAC0J,kBAAkB,CAAC,IAAI,CAACtM,KAAK,EAAEiG,MAAM,CAAC,CAAC;IAChS;EACF,CAAC,CAAC,EAAE,CAAC;IACHvJ,GAAG,EAAE,0BAA0B;IAC/BgF,KAAK,EAAE,SAAS6K,wBAAwBA,CAACC,SAAS,EAAEC,SAAS,EAAE;MAC7D,IAAID,SAAS,CAACrD,WAAW,KAAKsD,SAAS,CAACC,eAAe,EAAE;QACvD,OAAO;UACLA,eAAe,EAAEF,SAAS,CAACrD,WAAW;UACtCwD,SAAS,EAAEH,SAAS,CAACvG,MAAM;UAC3BuD,UAAU,EAAEiD,SAAS,CAACE;QACxB,CAAC;MACH;MACA,IAAIH,SAAS,CAACvG,MAAM,KAAKwG,SAAS,CAACE,SAAS,EAAE;QAC5C,OAAO;UACLA,SAAS,EAAEH,SAAS,CAACvG;QACvB,CAAC;MACH;MACA,OAAO,IAAI;IACb;EACF,CAAC,EAAE;IACDvJ,GAAG,EAAE,QAAQ;IACbgF,KAAK,EAAE,SAASmD,MAAMA,CAACd,KAAK,EAAEM,KAAK,EAAE;MACnC,IAAIuI,SAAS,GAAG7I,KAAK,CAAChH,MAAM,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC6G,MAAM,CAACpF,kBAAkB,CAACuF,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAGA,KAAK;MAC1F,IAAI8I,MAAM,GAAG,EAAE;MACf,KAAK,IAAIlQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0H,KAAK,EAAE,EAAE1H,CAAC,EAAE;QAC9BkQ,MAAM,GAAG,EAAE,CAACjJ,MAAM,CAACpF,kBAAkB,CAACqO,MAAM,CAAC,EAAErO,kBAAkB,CAACoO,SAAS,CAAC,CAAC;MAC/E;MACA,OAAOC,MAAM;IACf;EACF,CAAC,EAAE;IACDnQ,GAAG,EAAE,eAAe;IACpBgF,KAAK,EAAE,SAASsG,aAAaA,CAAC8E,MAAM,EAAE9M,KAAK,EAAE;MAC3C,IAAI+M,OAAO;MACX,IAAK,aAAa7K,KAAK,CAAC8K,cAAc,CAACF,MAAM,CAAC,EAAE;QAC9CC,OAAO,GAAG,aAAa7K,KAAK,CAACgF,YAAY,CAAC4F,MAAM,EAAE9M,KAAK,CAAC;MAC1D,CAAC,MAAM,IAAIqC,UAAU,CAACyK,MAAM,CAAC,EAAE;QAC7BC,OAAO,GAAGD,MAAM,CAAC9M,KAAK,CAAC;MACzB,CAAC,MAAM;QACL,IAAItD,GAAG,GAAGsD,KAAK,CAACtD,GAAG;UACjBkL,QAAQ,GAAGvL,wBAAwB,CAAC2D,KAAK,EAAElE,UAAU,CAAC;QACxD,IAAIoM,SAAS,GAAG1F,IAAI,CAAC,mBAAmB,EAAE,OAAOsK,MAAM,KAAK,SAAS,GAAGA,MAAM,CAAC5E,SAAS,GAAG,EAAE,CAAC;QAC9F6E,OAAO,GAAG,aAAa7K,KAAK,CAAC8E,aAAa,CAACtE,GAAG,EAAEtF,QAAQ,CAAC;UACvDV,GAAG,EAAEA;QACP,CAAC,EAAEkL,QAAQ,EAAE;UACXM,SAAS,EAAEA;QACb,CAAC,CAAC,CAAC;MACL;MACA,OAAO6E,OAAO;IAChB;EACF,CAAC,CAAC,CAAC;AACL,CAAC,CAAC5K,aAAa,CAAC;AAChB/D,eAAe,CAACkF,IAAI,EAAE,aAAa,EAAE,MAAM,CAAC;AAC5ClF,eAAe,CAACkF,IAAI,EAAE,cAAc,EAAE;EACpC2J,OAAO,EAAE,CAAC;EACVC,OAAO,EAAE,CAAC;EACV5E,YAAY,EAAE,KAAK;EACnB6E,SAAS,EAAE,IAAI;EACf5F,GAAG,EAAE,IAAI;EACT6F,UAAU,EAAE,MAAM;EAClBC,MAAM,EAAE,SAAS;EACjBrB,WAAW,EAAE,CAAC;EACdtD,IAAI,EAAE,MAAM;EACZzC,MAAM,EAAE,EAAE;EACVV,iBAAiB,EAAE,CAACpC,MAAM,CAACmK,KAAK;EAChClE,gBAAgB,EAAE,IAAI;EACtBJ,cAAc,EAAE,CAAC;EACjBC,iBAAiB,EAAE,IAAI;EACvBC,eAAe,EAAE,MAAM;EACvBoC,IAAI,EAAE,KAAK;EACXiC,KAAK,EAAE;AACT,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAnP,eAAe,CAACkF,IAAI,EAAE,iBAAiB,EAAE,UAAUkK,KAAK,EAAE;EACxD,IAAIxN,KAAK,GAAGwN,KAAK,CAACxN,KAAK;IACrBkG,KAAK,GAAGsH,KAAK,CAACtH,KAAK;IACnBC,KAAK,GAAGqH,KAAK,CAACrH,KAAK;IACnBsH,UAAU,GAAGD,KAAK,CAACC,UAAU;IAC7BC,UAAU,GAAGF,KAAK,CAACE,UAAU;IAC7BjH,OAAO,GAAG+G,KAAK,CAAC/G,OAAO;IACvBkH,QAAQ,GAAGH,KAAK,CAACG,QAAQ;IACzBC,aAAa,GAAGJ,KAAK,CAACI,aAAa;IACnCC,MAAM,GAAGL,KAAK,CAACK,MAAM;EACvB,IAAIzH,MAAM,GAAGpG,KAAK,CAACoG,MAAM;EACzB,IAAIH,MAAM,GAAG2H,aAAa,CAAC9I,GAAG,CAAC,UAAU6C,KAAK,EAAEE,KAAK,EAAE;IACrD,IAAInG,KAAK,GAAG2B,iBAAiB,CAACsE,KAAK,EAAElB,OAAO,CAAC;IAC7C,IAAIL,MAAM,KAAK,YAAY,EAAE;MAC3B,OAAO;QACLM,CAAC,EAAEtD,uBAAuB,CAAC;UACzB0K,IAAI,EAAE5H,KAAK;UACX6H,KAAK,EAAEN,UAAU;UACjBE,QAAQ,EAAEA,QAAQ;UAClBhG,KAAK,EAAEA,KAAK;UACZE,KAAK,EAAEA;QACT,CAAC,CAAC;QACFlB,CAAC,EAAErE,KAAK,CAACZ,KAAK,CAAC,GAAG,IAAI,GAAGyE,KAAK,CAAC6H,KAAK,CAACtM,KAAK,CAAC;QAC3CA,KAAK,EAAEA,KAAK;QACZmF,OAAO,EAAEc;MACX,CAAC;IACH;IACA,OAAO;MACLjB,CAAC,EAAEpE,KAAK,CAACZ,KAAK,CAAC,GAAG,IAAI,GAAGwE,KAAK,CAAC8H,KAAK,CAACtM,KAAK,CAAC;MAC3CiF,CAAC,EAAEvD,uBAAuB,CAAC;QACzB0K,IAAI,EAAE3H,KAAK;QACX4H,KAAK,EAAEL,UAAU;QACjBC,QAAQ,EAAEA,QAAQ;QAClBhG,KAAK,EAAEA,KAAK;QACZE,KAAK,EAAEA;MACT,CAAC,CAAC;MACFnG,KAAK,EAAEA,KAAK;MACZmF,OAAO,EAAEc;IACX,CAAC;EACH,CAAC,CAAC;EACF,OAAOzJ,aAAa,CAAC;IACnB+H,MAAM,EAAEA,MAAM;IACdG,MAAM,EAAEA;EACV,CAAC,EAAEyH,MAAM,CAAC;AACZ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}