{"ast": null, "code": "export var slice = Array.prototype.slice;\nexport default function (x) {\n  return typeof x === \"object\" && \"length\" in x ? x // Array, TypedArray, NodeList, array-like\n  : Array.from(x); // Map, Set, iterable, string, or anything else\n}", "map": {"version": 3, "names": ["slice", "Array", "prototype", "x", "from"], "sources": ["/Users/<USER>/Downloads/interview-final/frontend/node_modules/d3-shape/src/array.js"], "sourcesContent": ["export var slice = Array.prototype.slice;\n\nexport default function(x) {\n  return typeof x === \"object\" && \"length\" in x\n    ? x // Array, TypedArray, NodeList, array-like\n    : Array.from(x); // Map, Set, iterable, string, or anything else\n}\n"], "mappings": "AAAA,OAAO,IAAIA,KAAK,GAAGC,KAAK,CAACC,SAAS,CAACF,KAAK;AAExC,eAAe,UAASG,CAAC,EAAE;EACzB,OAAO,OAAOA,CAAC,KAAK,QAAQ,IAAI,QAAQ,IAAIA,CAAC,GACzCA,CAAC,CAAC;EAAA,EACFF,KAAK,CAACG,IAAI,CAACD,CAAC,CAAC,CAAC,CAAC;AACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}